import { useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';

const TroubleshootingFields = () => {
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const equipmentCategorySelection = useSelector(selectSelectionByKey('equipmentCategory'));
  const equipmentManufacturerSelection = useSelector(selectSelectionByKey('equipmentManufacturer'));
  const equipmentModelSelection = useSelector(selectSelectionByKey('equipmentModel'));
  const imo = useSelector(selectSelectionByKey("ship"))

  return (
    <>
      <EntitySearch
        title={'IMO number (optional)'}
        placeholder={`Enter IMO number`}
        selectionKey="ship"
        data={imo ? imo.name : ''}
      />

      <EntitySearch
        title={'Equipment'}
        placeholder={`Enter equipment`}
        selectionKey="equipmentCategory"
        data={equipmentCategorySelection ? equipmentCategorySelection.name : ''}
      />

      <EntitySearch
        title={'Make'}
        placeholder={`Enter Make`}
        selectionKey="equipmentManufacturer"
        data={equipmentManufacturerSelection ? equipmentManufacturerSelection.name : ''}
      />

      <EntitySearch
        title={'Model'}
        placeholder={`Enter Model`}
        selectionKey="equipmentModel"
        data={equipmentModelSelection ? equipmentModelSelection.name : ''}
      />

      <EntitySearch
        title={'Department type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default TroubleshootingFields;
