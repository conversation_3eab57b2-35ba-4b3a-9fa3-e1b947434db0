import { View, Text, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';

const TermsOfServiceScreen = () => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 py-2">
          <BackButton onBack={handleBack} label="" />
        </View>

        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
          <View className="pb-10">
            <Text className="text-3xl font-bold text-gray-900 mb-2">Terms of Service</Text>
            <Text className="text-sm text-gray-500 mb-8">Effective Date: June 14, 2025</Text>

            <View className="gap-y-6">
              {/* Welcome */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  Welcome to Navicater
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Navicater ("we," "our," or "us") is a social knowledge-sharing platform for
                  seafarers, maritime professionals, students, and aspirants. This Terms of Service
                  governs your use of our products, features, apps, services, and software
                  ("Platform"). By using Navicater, you agree to these terms. If you disagree,
                  please do not use the Platform.
                </Text>
              </View>

              {/* Section 1 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  1. User Eligibility and Account Registration
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  You may use Navicater only if you are at least 16 years old, have not been banned
                  before, and provide accurate details. You're responsible for your account and must
                  keep your profile up to date.
                </Text>
              </View>

              {/* Section 2 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  2. Platform Overview
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Navicater offers:
                  {'\n\n'}• Maritime Knowledge Sharing
                  {'\n'}• Professional Networking
                  {'\n'}• Community Forums & Mentor Support
                  {'\n'}• Maritime Tools & Info (e.g. port/ship data)
                </Text>
              </View>

              {/* Section 3 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  3. User Content and Conduct
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  You retain ownership of your content but grant us a license to use it. You must
                  follow applicable laws, avoid misuse, and respect others. Do not use bots or post
                  harmful or misleading material.
                  {'\n\n'}Disclaimer: Navicater is not responsible for user advice or content.
                </Text>
              </View>

              {/* Section 4 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  4. Content Monitoring and Safety
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  We monitor the platform and take action against harmful content or users. Users
                  are encouraged to report abuse or suspicious activity.
                </Text>
              </View>

              {/* Section 5 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  5. Data Accuracy and Source Disclaimer
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Maritime data is aggregated from public sources. We do not verify accuracy and
                  advise against using it for critical operations.
                </Text>
              </View>

              {/* Section 6 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  6. Privacy and Data Use
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  We collect and use data per our Privacy Policy. We do not sell personal data
                  without consent. Some content and settings may be public depending on your
                  preferences.
                </Text>
              </View>

              {/* Section 7 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  7. Professional Use and Profile Policy
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Profiles must reflect true credentials and experiences. We reserve the right to
                  verify and remove misleading profiles or promotional spam.
                </Text>
              </View>

              {/* Section 8 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  8. Platform Integrity and Security
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Do not disrupt or exploit the Platform. We maintain industry-standard security,
                  but cannot guarantee complete data safety or uptime.
                </Text>
              </View>

              {/* Section 9 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">9. Termination</Text>
                <Text className="text-base text-gray-700 leading-6">
                  We may suspend or terminate accounts for violations. You can delete your account
                  anytime.
                </Text>
              </View>

              {/* Section 10 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  10. Disclaimers and Limitation of Liability
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  Navicater is provided “as is.” We are not liable for user content, data accuracy,
                  or damages resulting from use of the Platform.
                </Text>
              </View>

              {/* Section 11 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  11. Changes to These Terms
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  We may update terms periodically and will notify users of significant changes.
                  Continued use means acceptance.
                </Text>
              </View>

              {/* Section 12 */}
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  12. Governing Law and Dispute Resolution
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  These terms are governed by the laws of Canada. Disputes will be resolved through
                  arbitration or local courts.
                </Text>
              </View>

              {/* Section 13 - Contact */}
              <View className="bg-green-50 rounded-xl p-4">
                <Text className="text-xl font-semibold text-green-800 mb-3">
                  13. Contact Information
                </Text>
                <Text className="text-base text-green-700 leading-6">
                  Questions or legal notices? Contact us:
                  {'\n\n'}📧 <EMAIL>
                </Text>
              </View>

              {/* Footer */}
              <View className="pt-6">
                <Text className="text-sm text-gray-500">
                  By using Navicater, you agree to these Terms of Service. Your continued
                  participation helps build a safer, smarter maritime world.
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default TermsOfServiceScreen;
