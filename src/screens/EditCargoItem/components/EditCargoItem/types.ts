import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdTypeI } from '@/src/types/common/data';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';

export interface EditCargoItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
  shipId: string;
  cargoId: string;
  preFilledData: ShipCreateEditPayloadI[];
  refetch: () => void;
  shipData: { fromDate: string; toDate: string | null };
}

export interface useEditCargoItemI {
  methods: UseFormReturn<CargoDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: CargoDetailsFormDataI) => Promise<void>;
  navigation: StackNavigationProp<HomeStackParamListI>;
  handlePresentCheckbox: () => void;
  isPresent: boolean;
  loading: boolean;
}

export type CargoDetailsFormDataI = {
  name: string;
  description: string;
  fromDate: string;
  toDate: string | null;
};

export type CargoCreateEditPayloadI = {
  opr?: 'UPDATE' | 'CREATE';
  name?: string;
  description?: string;
  fromDate?: string;
  toDate?: string | null;
  id?: string;
};

export type CargoCreateUpdatePayloadI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity?: SearchResultI;
  id?: string;
  designations: {
    designation?: SearchResultI;
    fromDate?: string;
    id?: string;
    opr?: string;
    ships?: {
      opr?: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
      ship?: {
        imo: string;
        dataType: string;
      };
      name?: string;
      sizeGt?: number;
      powerKw?: number;
      fromDate?: string;
      toDate?: string | null;
      details?: string;
      department?: IdTypeI;
      subVesselType?: IdTypeI;
      dwt?: number;
      id?: string;
      skills?: {
        opr: string;
        id: string;
        dataType: 'raw' | 'master';
      }[];
      cargos: {
        opr?: 'UPDATE' | 'CREATE';
        name?: string;
        description?: string;
        fromDate?: string;
        toDate?: string | null;
        id?: string;
      }[];
    };
    toDate?: string;
  }[];
};
