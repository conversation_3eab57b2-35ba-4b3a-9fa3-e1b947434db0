import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { addCargoShipExperience } from '@/src/redux/slices/experience/experienceSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { fetchSingleShipCargo } from '@/src/networks/experienceShip.ts/ship';
import { generatePayloadCargo } from '../utils';
import { CargoCreateUpdatePayloadI, CargoDetailsFormDataI, useEditCargoItemI } from './types';

export const useEditCargoItem = (
  profileId?: string,
  experienceId?: string,
  shipId?: string,
  cargoId?: string,
  onBack?: () => void,
  preFilledData?: ShipCreateEditPayloadI[],
  refetch?: () => void,
): useEditCargoItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const [loading, setLoading] = useState(false);
  const [isPresent, setIsPresent] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<CargoDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      description: '',
      fromDate: '',
      toDate: '',
    },
  });

  useEffect(() => {
    if (cargoId) {
      const fetchCargo = async () => {
        try {
          setLoading(true);
          const response = await fetchSingleShipCargo(cargoId);

          const fetchedCargo: CargoDetailsFormDataI = {
            name: response.name,
            description: response.description,
            fromDate: response.fromDate,
            toDate: response.toDate,
          };

          if (!response.toDate) {
            setIsPresent(true);
          }

          methods.reset(fetchedCargo);
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              `Failed to load cargo details: ${error instanceof Error ? error.message : 'Unknown error'}`,
            ),
          );
        } finally {
          setLoading(false);
        }
      };

      fetchCargo();
    }
  }, [cargoId, methods]);

  const onSubmit = async (data: CargoDetailsFormDataI) => {
    const payload: CargoCreateUpdatePayloadI = generatePayloadCargo(data, preFilledData!, cargoId);
    try {
      setIsSubmitting(true);
      await dispatch(addCargoShipExperience({ payload })).unwrap();

      showToast({
        message: 'Success',
        description: `Added new Cargo successfully`,
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Cargo',
            description: 'Unable to save Cargo',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      if (refetch) {
        refetch();
      }
    }
  };

  const handlePresentCheckbox = () => {
    const newPresentState = !isPresent;
    setIsPresent(newPresentState);
    if (newPresentState) {
      methods.setValue('toDate', null);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    handlePresentCheckbox,
    isPresent,
    loading,
  };
};
