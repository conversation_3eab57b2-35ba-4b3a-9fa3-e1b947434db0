import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import TextInput from '@/src/components/TextInput';
import { validateFromDate, validateToDate } from '../utils';
import { CargoDetailsFormDataI, EditCargoItemPropsI } from './types';
import { useEditCargoItem } from './useHook';

const EditCargoItem = ({
  onBack,
  profileId,
  experienceId,
  shipId,
  cargoId,
  preFilledData,
  refetch,
  shipData,
}: EditCargoItemPropsI) => {
  const { methods, onSubmit, isSubmitting, handlePresentCheckbox, isPresent, loading } =
    useEditCargoItem(profileId, experienceId, shipId, cargoId, onBack, preFilledData, refetch);
  const { control, handleSubmit, watch } = methods;

  const FromDate = watch('fromDate');
  const ToDate = watch('toDate');

  const renderField = (
    name: keyof Omit<CargoDetailsFormDataI, 'fromDate' | 'toDate'>,
    label: string,
  ) => (
    <Controller
      control={control}
      name={name}
      rules={{ required: `${label} is required` }}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <TextInput
          label={label}
          value={value}
          onChangeText={onChange}
          placeholder={`Enter ${label.toLowerCase()}`}
          error={error?.message}
          className="py-3"
          key={label}
        />
      )}
    />
  );

  if (loading) {
    return (
      <View>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-4 text-gray-600">Loading cargo details...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="px-4">
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Edit Cargo" />
        <Pressable onPress={handleSubmit(onSubmit)} disabled={isSubmitting}>
          <Text className="text-lg font-medium text-[#448600]">
            {isSubmitting ? 'Submitting' : 'Save'}
          </Text>
        </Pressable>
      </View>
      {renderField('name', 'Cargo Name')}
      {renderField('description', 'Description')}
      <View className="flex-row mb-6">
        <View className="flex-1 mr-2">
          <Controller
            control={control}
            name="fromDate"
            rules={{
              validate: (value) =>
                validateFromDate(value, ToDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="From"
                  selectedDate={FromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1 ml-2">
          <Controller
            control={control}
            name="toDate"
            rules={{
              validate: (value) =>
                validateToDate(value, isPresent, FromDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="To"
                  selectedDate={isPresent ? null : ToDate}
                  onDateChange={(date) => {
                    if (date instanceof Date && !isPresent) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  disabled={isPresent}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
          <Checkbox
            label="Unlimited"
            className="pt-3"
            labelClassName="text-base text-sm"
            onValueChange={handlePresentCheckbox}
            checked={isPresent}
          />
        </View>
      </View>
    </View>
  );
};

export default EditCargoItem;
