import type React from 'react';
import { object } from 'zod';

export interface SettingsSectionProps {
  title: string;
  children: React.ReactNode;
}

export interface SettingsItemProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  rightElement?: React.ReactNode;
  onPress?: () => void;
  isLast?: boolean;
}

export interface ToggleSettingProps extends Omit<SettingsItemProps, 'rightElement'> {
  value: boolean;
  onValueChange: (value: boolean) => void;
}

export interface UserSettingsProps {
  onBack: () => void;
}

export type SettingsState = object;

export interface UseSettingsHookReturn {
  settings: SettingsState;
  isLoading: boolean;
  signOut: () => Promise<void>;
  isSignOutModalVisible: boolean;
  isDeleteAccountModalVisible: boolean;
  handleConfirmSignOut: () => Promise<void>;
  handleCancelSignOut: () => void;
  handleConfirmDeleteAccount: () => Promise<void>;
  handleCancelDeleteAccount: () => void;
}
