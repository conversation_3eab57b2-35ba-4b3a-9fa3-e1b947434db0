import { ForumQuestionI } from "@/src/networks/question/types";

/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type UseForumPostListResult = {
  questions: ForumQuestionI[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  isLive: boolean;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  toggleLiveMode: () => void;
};