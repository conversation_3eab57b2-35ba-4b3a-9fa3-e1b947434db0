import React from 'react';
import { Pressable, Text, View, Image } from 'react-native';
import BackButton from '@/src/components/BackButton';
import UserAvatar from '@/src/components/UserAvatar';
import AiBot from '@/src/assets/images/others/aibot.png';

const TopBar: React.FC<{}> = React.memo(() => (
  <View className="flex-row justify-between items-center px-4 py-2 bg-white">
    <View className="flex-row items-center">
      <BackButton onBack={() => {}} label="" />
      <Text className="text-2xl font-semibold text-black">Forum</Text>
    </View>
    <View className="flex-row items-center gap-2">
      <Pressable className="items-center justify-center pr-2">
        <UserAvatar
          avatarUri={Image.resolveAssetSource(AiBot).uri}
          width={37}
          height={37}
          name="Navicater AI"
        />
      </Pressable>
    </View>
  </View>
));

export default TopBar;
