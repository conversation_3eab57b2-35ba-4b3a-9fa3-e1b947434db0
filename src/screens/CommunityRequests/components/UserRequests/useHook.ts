/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { UseUserRequestsPropsI, UserRequestsHookResultI, ForumJoinRequestI } from './types';

const mockJoinRequests: ForumJoinRequestI[] = [
  {
    id: '1',
    Profile: {
      id: '1',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON>',
      designation: {
        id: '1',
        name: 'Marine Engineer',
      },
      entity: {
        id: '1',
        name: 'Ocean Dynamics',
      },
    },
    requestedAt: '2024-03-20',
    message: 'I would like to join this forum to share my maritime experience.',
    status: 'PENDING',
  },
  {
    id: '2',
    Profile: {
      id: '2',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON>',
      designation: {
        id: '2',
        name: 'Navigation Officer',
      },
      entity: {
        id: '2',
        name: 'Maritime Solutions',
      },
    },
    requestedAt: '2024-03-19',
    message: 'Looking forward to connecting with fellow maritime professionals.',
    status: 'PENDING',
  },
  {
    id: '3',
    Profile: {
      id: '3',
      avatar:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
      name: 'Mike Chen',
      designation: {
        id: '3',
        name: 'Chief Engineer',
      },
      entity: {
        id: '3',
        name: 'Global Shipping',
      },
    },
    requestedAt: '2024-03-18',
    message: 'Interested in participating in technical discussions.',
    status: 'PENDING',
  },
];

export const useUserRequests = ({
  forumId,
  pageSize = 20,
}: UseUserRequestsPropsI): UserRequestsHookResultI => {
  const [data, setData] = useState<ListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);
  const [requestStatus, setRequestStatus] = useState<
    Record<string, 'ACCEPTED' | 'REJECTED' | undefined>
  >({});

  const transformRequestsToListItems = (requests: ForumJoinRequestI[]): ListItem[] => {
    return requests.map((request) => ({
      Profile: request.Profile,
      status: request.status,
    })) as ListItem[];
  };

  const fetchUserRequests = useCallback(
    async (pageNum: number, isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));

        const pendingRequests = mockJoinRequests.filter((req) => req.status === 'PENDING');
        const startIndex = (pageNum - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedRequests = pendingRequests.slice(startIndex, endIndex);

        const transformedData = transformRequestsToListItems(paginatedRequests);

        if (isRefresh || pageNum === 1) {
          setData(transformedData);
        } else {
          setData((prev) => [...prev, ...transformedData]);
        }

        setHasMore(endIndex < pendingRequests.length);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [pageSize],
  );

  const handleRequest = useCallback(async (item: ListItem, status: 'ACCEPTED' | 'REJECTED') => {
    const profileId = item.Profile?.id;
    if (!profileId) return;

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setRequestStatus((prev) => ({ ...prev, [profileId]: status }));

      setTimeout(() => {
        setData((prev) => prev.filter((dataItem) => dataItem.Profile.id !== profileId));
      }, 2000);

      console.log(`${status} request for user: ${item.Profile.name}`);
    } catch (err) {
      console.error('Failed to handle request:', err);
      setError(err as Error);
    }
  }, []);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchUserRequests(nextPage);
    }
  }, [loading, hasMore, page, fetchUserRequests]);

  const refresh = useCallback(() => {
    setPage(1);
    setRequestStatus({});
    fetchUserRequests(1, true);
  }, [fetchUserRequests]);

  useEffect(() => {
    fetchUserRequests(1);
  }, [fetchUserRequests]);

  return {
    data,
    loading,
    refreshing,
    hasMore,
    error,
    loadMore,
    refresh,
    handleRequest,
    requestStatus,
  };
};
