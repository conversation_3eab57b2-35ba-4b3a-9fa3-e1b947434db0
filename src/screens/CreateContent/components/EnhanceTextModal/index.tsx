import { useState, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  ScrollView,
  TextInput,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import Modal from 'react-native-modal';
import BottomSheet from '@/src/components/Bottomsheet';
import type { EnhanceTextModalProps } from './types';

const EnhanceTextModal = ({ visible, onClose, onSubmit, currentText }: EnhanceTextModalProps) => {
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (visible) {
      setShowCustomPrompt(false);
      setCustomPrompt('');
    }
  }, [visible]);

  const handleCustomPromptSubmit = () => {
    if (customPrompt.trim()) {
      const enhancedPrompt = `Based on this text: "${currentText}", ${customPrompt.trim()}`;
      onSubmit(enhancedPrompt);
      setCustomPrompt('');
      setShowCustomPrompt(false);
      onClose();
    }
  };

  const handleCustomPromptOpen = () => {
    onClose();
    setTimeout(() => {
      setShowCustomPrompt(true);
    }, 800);
  };

  const handleCustomPromptClose = () => {
    setCustomPrompt('');
    setShowCustomPrompt(false);
  };

  const getBottomSheetHeight = () => {
    return 400;
  };

  return (
    <>
      <BottomSheet
        onModalHide={() => {}}
        visible={visible && !showCustomPrompt}
        onClose={onClose}
        height={getBottomSheetHeight()}
        backdropOpacity={0.5}
      >
        <View className="flex-1 px-4 py-2">
          <View className="flex-row justify-between items-center mb-4 pt-2">
            <Text className="text-lg font-semibold text-gray-900">Enhance Your Text</Text>
            <Pressable
              onPress={onClose}
              className="p-2 rounded-full active:bg-gray-100"
              hitSlop={8}
            >
              <Text className="text-gray-500 text-lg">✕</Text>
            </Pressable>
          </View>

          <View className="bg-gray-50 p-3 rounded-lg mb-4">
            <Text className="text-sm text-gray-600 mb-1">Current text:</Text>
            <Text className="text-sm text-gray-900" numberOfLines={2}>
              {currentText}
            </Text>
          </View>

          <ScrollView
            className="flex-1"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            nestedScrollEnabled={true}
          >
            <Pressable
              onPress={() => {
                const enhancedPrompt = `Improve and expand this maritime text: "${currentText}"`;
                onSubmit(enhancedPrompt);
                onClose();
              }}
              className="p-4 bg-violet-50 rounded-lg border border-violet-200 active:bg-violet-100 mb-3"
              android_ripple={{ color: 'rgba(124, 58, 237, 0.1)' }}
            >
              <Text className="text-violet-600 text-sm font-medium text-center">
                ✨ General Enhancement
              </Text>
            </Pressable>

            <Pressable
              onPress={() => {
                const enhancedPrompt = `Make this maritime text more professional and technical: "${currentText}"`;
                onSubmit(enhancedPrompt);
                onClose();
              }}
              className="p-4 bg-blue-50 rounded-lg border border-blue-200 active:bg-blue-100 mb-3"
              android_ripple={{ color: 'rgba(59, 130, 246, 0.1)' }}
            >
              <Text className="text-blue-600 text-sm font-medium text-center">
                🔧 Technical Enhancement
              </Text>
            </Pressable>

            <Pressable
              onPress={handleCustomPromptOpen}
              className="p-4 bg-orange-50 rounded-lg border border-orange-200 active:bg-orange-100"
              android_ripple={{ color: 'rgba(251, 146, 60, 0.1)' }}
            >
              <Text className="text-orange-600 text-sm font-medium text-center">
                ✏️ Custom Enhancement
              </Text>
            </Pressable>
          </ScrollView>
        </View>
      </BottomSheet>

      <Modal
        isVisible={showCustomPrompt}
        onBackdropPress={handleCustomPromptClose}
        onBackButtonPress={handleCustomPromptClose}
        animationIn="fadeIn"
        animationOut="fadeOut"
        backdropOpacity={0.5}
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard={Platform.OS === 'ios'}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: 1, justifyContent: 'center' }}
        >
          <View className="justify-center items-center px-4">
            <View className="bg-white rounded-lg p-6 w-full max-w-sm">
              <View className="flex-row justify-between items-center mb-4">
                <Text className="text-lg font-semibold text-gray-900">Custom Enhancement</Text>
                <Pressable
                  onPress={handleCustomPromptClose}
                  className="p-2 rounded-full active:bg-gray-100"
                  hitSlop={8}
                >
                  <Text className="text-gray-500 text-lg">✕</Text>
                </Pressable>
              </View>

              <View className="bg-gray-50 p-3 rounded-lg mb-4">
                <Text className="text-sm text-gray-600 mb-1">Current text:</Text>
                <Text className="text-sm text-gray-900" numberOfLines={2}>
                  {currentText}
                </Text>
              </View>

              <Text className="text-sm text-gray-600 mb-3">
                Describe how you want to enhance your text:
              </Text>

              <TextInput
                value={customPrompt}
                onChangeText={setCustomPrompt}
                placeholder="e.g., make it more technical, add safety tips, include regulations..."
                placeholderTextColor="#9ca3af"
                className="border border-gray-300 rounded-lg p-3 text-base text-gray-900 mb-4"
                multiline
                maxLength={1000}
                textAlignVertical="top"
                autoFocus
                style={{ minHeight: 100, maxHeight: 150 }}
                scrollEnabled={true}
              />

              <View className="flex-row gap-3">
                <Pressable
                  onPress={handleCustomPromptClose}
                  className="flex-1 py-3 px-4 bg-gray-100 rounded-lg active:bg-gray-200"
                  android_ripple={{ color: 'rgba(0, 0, 0, 0.05)' }}
                >
                  <Text className="text-center text-gray-700 font-medium">Cancel</Text>
                </Pressable>

                <Pressable
                  onPress={handleCustomPromptSubmit}
                  className={`flex-1 py-3 px-4 rounded-lg ${
                    customPrompt.trim() ? 'bg-violet-600 active:bg-violet-700' : 'bg-gray-300'
                  }`}
                  disabled={!customPrompt.trim()}
                  android_ripple={{
                    color: customPrompt.trim() ? 'rgba(124, 58, 237, 0.2)' : 'transparent',
                  }}
                >
                  <Text
                    className={`text-center font-medium ${customPrompt.trim() ? 'text-white' : 'text-gray-500'}`}
                  >
                    Enhance
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
};

export default EnhanceTextModal;
