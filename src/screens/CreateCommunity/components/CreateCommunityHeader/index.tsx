import { Pressable, Text, View } from 'react-native';
import Button from '@/src/components/Button';
import Close from '@/src/assets/svgs/Close';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const TOTAL_PAGES = 3;

const CreateCommunityHeader = ({
  currentPage,
  buttonTitle,
  onNext,
}: {
  currentPage: number;
  onNext: () => void;
  buttonTitle?: string;
}) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>()
  const onClose = () => {
    navigation.goBack()
  }
  return (
    <View className="flex-row justify-between">
      <View className="flex-row items-center gap-3">
        <Pressable onPress={onClose}>
          <Close width={2.25} height={2.25} />
        </Pressable>
        <Text className="text-xl font-medium">
          {currentPage}/{TOTAL_PAGES}
        </Text>
      </View>
      <View>
        <Button
          className="rounded-full bg-primaryGreen w-auto px-6"
          onPress={onNext}
          label={buttonTitle ?? 'Next'}
        ></Button>
      </View>
    </View>
  );
};

export default CreateCommunityHeader;
