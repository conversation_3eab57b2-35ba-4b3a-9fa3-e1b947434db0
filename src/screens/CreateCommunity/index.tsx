import { KeyboardAvoidingView, Platform, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';
import CreateCommunityForm from './components/CreateCommunityForm';
import CreateCommunityHeader from './components/CreateCommunityHeader';

const CreateCommunityScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const handleNext = () => {
    navigation.navigate('Home');
  };
  return (
    <SafeArea>
      <KeyboardAvoidingView
        {...(Platform.OS === 'ios' ? { behavior: 'padding' } : {})}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="always">
          <View className="flex-1 px-5">
            <CreateCommunityHeader currentPage={1} onNext={() => handleNext()} />
            <CreateCommunityForm />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default CreateCommunityScreen;
