import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { addEquipmentCategoryShipExperience } from '@/src/redux/slices/experience/experienceSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { fetchSingleShipEquipmentCategory } from '@/src/networks/experienceShip.ts/ship';
import { generatePayloadEquipment } from '../utils';
import {
  EquipmentCreateUpdatePayloadI,
  EquipmentDetailsFormDataI,
  useEditEquipmentItemI,
} from './types';

export const useEditEquipmentItem = (
  profileId?: string,
  experienceId?: string,
  shipId?: string,
  equipmentId?: string,
  onBack?: () => void,
  preFilledData?: ShipCreateEditPayloadI[],
  refetch?: () => void,
): useEditEquipmentItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const [localFuelTypes, setLocalFuelTypes] = useState<SearchResultI[]>([]);
  const fuelTypesSelection = useSelector(selectMultipleSelectionsByKey('fuelType'));
  const [initialFuelTypes, setInitialFuelTypes] = useState<SearchResultI[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<EquipmentDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      category: {},
      manufacturerName: '',
      model: '',
    },
  });

  useEffect(() => {
    if (equipmentId) {
      const fetchEquipment = async () => {
        try {
          setLoading(true);
          const response = await fetchSingleShipEquipmentCategory(equipmentId);
          const fetchedEquipment: EquipmentDetailsFormDataI = {
            category: response.equipmentCategory,
            manufacturerName: response.manufacturerName,
            model: response.model,
            power: response.powerCapacity,
            fuelType: response.fuelTypes,
            additionalDetails: response.details!,
          };

          setLocalFuelTypes(response.fuelTypes?.length > 0 ? response.fuelTypes : []);
          setInitialFuelTypes(response.fuelTypes?.length > 0 ? response.fuelTypes : []);
          methods.reset(fetchedEquipment);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              'Failed to load equipment details: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };

      fetchEquipment();
    }
  }, [equipmentId, methods]);

  useEffect(() => {
    if (!fuelTypesSelection) return;

    setLocalFuelTypes((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...fuelTypesSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [fuelTypesSelection]);

  const onSubmit = async (data: EquipmentDetailsFormDataI) => {
    const payload: EquipmentCreateUpdatePayloadI = generatePayloadEquipment(
      data,
      preFilledData!,
      localFuelTypes,
      initialFuelTypes,
      equipmentId,
    );
    try {
      setIsSubmitting(true);
      await dispatch(addEquipmentCategoryShipExperience({ payload })).unwrap();
      showToast({
        message: 'Success',
        description: `Added new Equipment Category successfully`,
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Equipment Category',
            description: 'Unable to save Equipment Category',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      if (refetch) {
        refetch();
      }
    }
  };

  const clearFields = () => {
    dispatch(clearSelection('category'));
    setIsSubmitted(false);
    setLocalFuelTypes([]);
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localFuelTypes,
    setLocalFuelTypes,
    clearFields,
    isSubmitted,
    setIsSubmitted,
    loading,
  };
};
