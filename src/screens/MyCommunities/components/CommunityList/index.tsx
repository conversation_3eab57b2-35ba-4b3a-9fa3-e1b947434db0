import { <PERSON><PERSON><PERSON>, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import CommunityItem from '../CommunityItem';
import { CommunityI } from './types';

const sampleCommunities: CommunityI[] = [
  {
    id: '1',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '2',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '3',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '4',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
];

const CommunityList = () => {
  const navigation = useNavigation();
  const onBack = () => {
    navigation.goBack();
  };

  const renderCommunityItem = ({ item }: { item: CommunityI }) => (
    <CommunityItem
      community={item}
      // onPress={() => onCommunityPress?.(item)}
    />
  );
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <BackButton
        onBack={onBack}
        label="My communities"
        labelClassname="font-medium text-xl leading-6"
      />
      <FlatList
        data={sampleCommunities}
        renderItem={renderCommunityItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        className="flex-1"
      />
    </>
  );
};

export default CommunityList;
