import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
import { CargoPropsI, ShipCargoI } from './types';
import { useCargo } from './useHook';

const Cargo = ({
  profileId,
  shipId,
  isAddVisible,
  handleAdd,
  fromProfileExperience,
  shipData,
}: CargoPropsI) => {
  const {
    cargos,
    handleAddEditCargo,
    loading,
    handleDelete,
    isDeleting,
    isVisible,
    setIsVisible,
    setDeleteCargoId,
  } = useCargo(handleAdd, shipId, shipData);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center py-10">
        <ActivityIndicator size="small" color={'#448600'} />
      </View>
    );
  }

  const renderCargoItem = (item: ShipCargoI) => (
    <View key={item.id} className="py-3 pb-3">
      <Text className="text-base font-inter font-medium">{item.name}</Text>
      <View className="flex-row item-center justify-between">
        <Text className="text-base font-inter ">{`${toMonthYear(item.fromDate)} - ${item.toDate ? toMonthYear(item.toDate) : 'Present'}`}</Text>
        {!fromProfileExperience && (
          <View className="flex-row items-center gap-4">
            <Pressable
              onPress={() => {
                const payload = handleAdd();
                handleAddEditCargo(payload, item.id);
              }}
            >
              <EditPencil width={2} height={2} />
            </Pressable>
            <Pressable
              onPress={() => {
                setDeleteCargoId(item.id);
                setIsVisible(true);
              }}
            >
              <DeleteIcon width={2} height={2} />
            </Pressable>
          </View>
        )}
      </View>
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this equipment category?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          const payload = handleAdd();
          handleDelete(payload);
        }}
        isConfirming={isDeleting}
      />
    </View>
  );

  return (
    <>
      {cargos.map((item) => renderCargoItem(item))}
      {isAddVisible && !fromProfileExperience && (
        <View>
          <Pressable
            className="flex-row items-center gap-5 py-5"
            onPress={() => {
              const payload = handleAdd();
              handleAddEditCargo(payload);
            }}
          >
            <AddItem />
            <Text className="text-[#448600] text-lg font-medium">New Cargo</Text>
          </Pressable>
        </View>
      )}
    </>
  );
};

export default Cargo;
