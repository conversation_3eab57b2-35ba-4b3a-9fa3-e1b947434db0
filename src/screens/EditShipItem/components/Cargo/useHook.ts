import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { addShipExperience } from '@/src/redux/slices/experience/experienceSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { fetchShipCargos } from '@/src/networks/experienceShip.ts/ship';
import { ShipCreateEditPayloadI } from '../EditShipItem/types';
import { ShipCargoI, UseCargoI } from './types';

export const useCargo = (
  handleAdd: () => void,
  shipId: string,
  shipData: { fromDate: string; toDate: string | null },
) => {
  const [cargos, setCargos] = useState<ShipCargoI[]>([]);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [deleteCargoId, setDeleteCargoId] = useState<string | null>();
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useEffect(() => {
    fetchCargos();
  }, [shipId]);

  const fetchCargos = async () => {
    try {
      setLoading(true);
      const response = await fetchShipCargos({ experienceShipId: shipId });
      setCargos(response);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load cargos: ' + (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchCargos();
  };

  const handleAddEditCargo = (payload?: ShipCreateEditPayloadI[], cargoId?: string) => {
    if (payload) {
      navigation.navigate('EditCargoItem', {
        profileId: '',
        experienceId: '',
        shipId: shipId,
        cargoId: cargoId,
        data: payload,
        refetch: refetch,
        shipData: shipData,
      });
    }
  };

  const handleDelete = async (preFilledData: ShipCreateEditPayloadI[]) => {
    try {
      setIsDeleting(true);
      const cargo = {
        opr: 'DELETE',
        id: deleteCargoId,
      };
      let payload = JSON.parse(JSON.stringify(preFilledData));
      payload[0].designations[0].ships[0]['cargos'] = [cargo];

      await dispatch(addShipExperience({ payload })).unwrap();
      setCargos((prev) => prev.filter((item) => item.id !== deleteCargoId));
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Delete Cargo',
            description: 'Failed to Delete',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
      setIsVisible(false);
      setDeleteCargoId(null);
    }
  };

  return {
    loading,
    cargos,
    handleAddEditCargo,
    handleDelete,
    isDeleting,
    isVisible,
    setIsVisible,
    setDeleteCargoId,
  };
};
