import { SetStateAction, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Pressable,
  ScrollView,
  Text,
  View,
  Platform,
  Keyboard,
} from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import Tabs from '@/src/components/Tabs';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import Cargo from '../Cargo';
import Equipment from '../Equipment';
import { validateFromDate, validateToDate } from '../utils';
import {
  EditShipItemPropsI,
  ShipDetailsFormDataI,
  ShipEntitySearchResultI,
  ShipTabsI,
  ShipTypeEntitySearchResultI,
} from './types';
import { useEditShipItem } from './useHook';

const EditShipItem = ({
  onBack,
  profileId,
  shipId,
  preFilledData,
  field,
  fromProfileExperience,
  refetch,
}: EditShipItemPropsI) => {
  const {
    methods,
    onSubmit,
    localSkills,
    setLocalSkills,
    isPresent,
    handlePresentCheckbox,
    populateData,
    isAddVisible,
    isSubmitting,
    clearFields,
    handleAddEquipmentCargo,
    equipments,
    shipReFetch,
    loading,
    isSubmitted,
    setIsSubmitted,
  } = useEditShipItem(preFilledData, field, profileId, shipId, fromProfileExperience, refetch);

  const { control, handleSubmit } = methods;

  const handleBack = () => {
    clearFields();
    onBack();
  };

  const tabs = [
    {
      id: 'equipment',
      label: 'Equipment',
    },
    {
      id: 'cargo',
      label: 'Cargo',
    },
  ];

  const FromDate = methods.watch('fromDate');
  const ToDate = methods.watch('toDate');

  const tabScreens: ShipTabsI = {
    equipment: (
      <Equipment
        profileId={profileId}
        shipId={shipId!}
        isAddVisible={isAddVisible}
        handleAdd={handleAddEquipmentCargo}
        equipments={equipments}
        fromProfileExperience={fromProfileExperience !== undefined}
        refetch={shipReFetch}
      />
    ),
    cargo: (
      <Cargo
        profileId={profileId}
        shipId={shipId!}
        isAddVisible={isAddVisible}
        handleAdd={handleAddEquipmentCargo}
        fromProfileExperience={fromProfileExperience !== undefined}
        shipData={{ fromDate: FromDate, toDate: ToDate }}
      />
    ),
  };

  const [activeTab, setActiveTab] = useState<keyof ShipTabsI>('equipment');
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(e.endCoordinates.height);
      }
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(0);
      }
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const textFields: { name: keyof ShipDetailsFormDataI; label: string }[] = [
    {
      name: 'imoNumber',
      label: 'IMO Number',
    },
    {
      name: 'shipName',
      label: 'Ship Name',
    },
    {
      name: 'subVesselType',
      label: 'Ship Type',
    },
    {
      name: 'grossTonnage',
      label: 'Gross Tonnage',
    },
    {
      name: 'deadWeight',
      label: 'Deadweight(t)',
    },
    {
      name: 'power',
      label: 'Power(kW)',
    },
    {
      name: 'additionalDetails',
      label: 'Additional Details',
    },
    {
      name: 'department',
      label: 'Department',
    },
  ];

  const shipSelection = useSelector(
    selectSelectionByKey('ship'),
  ) as unknown as ShipEntitySearchResultI & { id?: string };
  const subVesselTypeSelection = useSelector(
    selectSelectionByKey('subVesselType'),
  ) as unknown as ShipTypeEntitySearchResultI;
  const departmentSelection = useSelector(selectSelectionByKey('department'));

  useEffect(() => {
    if (shipSelection) {
      const imo = shipSelection.imo || shipSelection.id || '';
      const shipName = shipSelection.matchedName || shipSelection.name || '';

      if (imo) {
        methods.setValue('imoNumber', { imo, dataType: shipSelection.dataType });
        methods.setValue('shipName', shipName);
        populateData({ imo, dataType: shipSelection.dataType });
      }
    }
    if (subVesselTypeSelection) {
      methods.setValue('subVesselType', subVesselTypeSelection);
    }
    if (departmentSelection) {
      methods.setValue('department', departmentSelection);
    }

    return () => {};
  }, [shipSelection, subVesselTypeSelection, departmentSelection, methods, populateData]);

  const renderField = (name: keyof ShipDetailsFormDataI, label: string) => {
    if (name === 'imoNumber') {
      return (
        <EntitySearch
          key={name}
          title={label}
          placeholder={`Enter ${label.toLowerCase()}`}
          selectionKey="ship"
          data={
            shipSelection ? shipSelection.imo || shipSelection.id : methods.watch('imoNumber')?.imo
          }
          editable={fromProfileExperience === undefined}
          error={
            isSubmitted && !shipSelection && !methods.watch('imoNumber')?.imo
              ? `${label} is required`
              : undefined
          }
        />
      );
    }
    if (name === 'subVesselType') {
      return (
        <EntitySearch
          key={name}
          title={label}
          placeholder={`Enter ${label.toLowerCase()}`}
          selectionKey="subVesselType"
          data={
            subVesselTypeSelection
              ? subVesselTypeSelection.name
              : methods.watch('subVesselType')?.name
          }
          editable={fromProfileExperience === undefined}
          error={
            isSubmitted && !subVesselTypeSelection && !methods.watch('subVesselType')?.id
              ? `${label} is required`
              : undefined
          }
        />
      );
    }
    if (name === 'department') {
      return (
        <EntitySearch
          key={name}
          title={label}
          placeholder={`Enter ${label.toLowerCase()}`}
          selectionKey="department"
          data={departmentSelection ? departmentSelection.name : methods.watch('department')?.name}
          editable={fromProfileExperience === undefined}
          error={
            isSubmitted && !departmentSelection && !methods.watch('department')?.id
              ? `${label} is required`
              : undefined
          }
        />
      );
    }
    const isRequired = name !== 'additionalDetails' && name !== 'deadWeight' && name !== 'power';
    const placeholder = isRequired
      ? `Enter ${label.toLowerCase()}`
      : name === 'deadWeight'
        ? 'Enter deadweight(t)'
        : 'Enter additional details like TEU, No. Of Cars...';

    return (
      <Controller
        key={name}
        control={control}
        name={name}
        rules={isRequired ? { required: `${label} is required` } : {}}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <TextInput
            label={label}
            value={value as string | undefined}
            onChangeText={onChange}
            placeholder={fromProfileExperience === undefined ? placeholder : ''}
            error={error?.message}
            className="py-3"
            editable={fromProfileExperience === undefined}
          />
        )}
      />
    );
  };

  if (loading) {
    return <ActivityIndicator size={'large'} className="flex-1" color={'#448600'} />;
  }

  const isAndroid15Plus = Platform.OS === 'android' && Number(Platform.Version) >= 35;

  const paddingBottom =
    isAndroid15Plus && keyboardHeight > 0 ? Math.max(keyboardHeight - 50, 0) : 0;

  return (
    <ScrollView
      className="flex-1 bg-white px-4"
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      contentContainerStyle={{
        paddingBottom: paddingBottom,
      }}
    >
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={handleBack} label="Edit Ship" />
        {fromProfileExperience === undefined && (
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting}
          >
            <Text className="text-lg font-medium text-[#448600]">{`${isSubmitting ? 'Saving' : 'Save'}`}</Text>
          </Pressable>
        )}
      </View>
      {textFields.map((textField) => renderField(textField.name, textField.label))}
      <View className="flex-row mb-6">
        <View className="flex-1 mr-2">
          <Controller
            control={control}
            name="fromDate"
            rules={{
              required: 'Start date is required',
              validate: (value) => validateFromDate(value, ToDate, field.fromDate, field.toDate),
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="From"
                  selectedDate={FromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  disabled={fromProfileExperience !== undefined}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1 ml-2">
          <Controller
            control={control}
            name="toDate"
            rules={{
              validate: (value) =>
                validateToDate(value, isPresent, FromDate, field.fromDate, field.toDate),
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="To"
                  selectedDate={isPresent ? null : ToDate}
                  onDateChange={(date) => {
                    if (date instanceof Date && !isPresent) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  disabled={fromProfileExperience !== undefined || isPresent}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
          <Checkbox
            label="Present"
            className="pt-3"
            labelClassName="text-base text-sm"
            onValueChange={handlePresentCheckbox}
            checked={isPresent}
            disabled={fromProfileExperience !== undefined}
          />
        </View>
      </View>
      <View className="mb-6">
        <ChipInput
          title="Skills"
          placeholder="Add a skill"
          chips={localSkills}
          onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
          disabled={fromProfileExperience !== undefined || localSkills.length >= 6}
          removable={fromProfileExperience === undefined}
        />
        {localSkills.length >= 6 && (
          <Text className="text-red-500 text-xs mt-1">
            You've reached the maximum of 6 skills. Remove some to add new ones.
          </Text>
        )}
      </View>
      <View>
        <Text className="text-md font-bold pb-4">More Details</Text>
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </View>
    </ScrollView>
  );
};

export default EditShipItem;
