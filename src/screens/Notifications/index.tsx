/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import NotificationList from './components/NotificationList';

const NotificationScreen = () => {
  const navigation = useNavigation();

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton onBack={() => navigation.goBack()} label="" />
      </View>
      <NotificationList />
    </SafeArea>
  );
};

export default NotificationScreen;
