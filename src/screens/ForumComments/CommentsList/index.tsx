/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable, FlatList } from 'react-native';
import ForumPost from '../../Forum/components/ForumPost';
import Answers from '../../ForumAnswers/Answers';
import CommentItem from '../CommentItem';
import type { ForumCommentProps } from '../CommentItem/types';

type CommentsListProps = {
  comments: ForumCommentProps[];
  onReply: (comment: ForumCommentProps) => void;
};

const CommentsList: React.FC<CommentsListProps> = ({ comments, onReply }) => (
  <View className="flex-1 px-2">
    <FlatList
      data={comments}
      keyExtractor={(item) => item.commentId}
      renderItem={({ item }) => <CommentItem comment={item} onReply={() => onReply(item)} />}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        flexGrow: 1,
        backgroundColor: 'white',
        paddingVertical: 20,
      }}
    />
  </View>
);

export default CommentsList;
