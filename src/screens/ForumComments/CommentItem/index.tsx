/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import type { ForumCommentProps } from './types';

const CommentItem: React.FC<{ comment: ForumCommentProps; onReply?: () => void }> = ({
  comment,
  onReply,
}) => (
  <View className="bg-white overflow-hidden p-2 border-b border-gray-200 rounded-lg">
    <View className=" flex-row items-center">
      <Text className="text-base font-normal text-[#6E6E6E]">
        {comment.content}
        <Text className="text-base font-medium text-[#6E6E6E]">
          {' - @'}
          {comment.userId}
        </Text>
      </Text>
    </View>
    <View className="p-2">
      <Pressable onPress={onReply}>
        <Text className="text-subLabelGray text-xs">Reply</Text>
      </Pressable>
    </View>
    {comment.replies && comment.replies.length > 0 && (
      <View>
        {comment.replies.map((reply) => (
          <View key={reply.replyId} className="flex-row items-center flex-wrap p-2">
            <Text className="text-base font-normal text-[#6E6E6E] px-2">
              {reply.content}
              <Text className="text-base font-medium text-[#6E6E6E]">
                {' - @'}
                {reply.userId}
              </Text>
            </Text>
          </View>
        ))}
      </View>
    )}
  </View>
);

export default CommentItem;
