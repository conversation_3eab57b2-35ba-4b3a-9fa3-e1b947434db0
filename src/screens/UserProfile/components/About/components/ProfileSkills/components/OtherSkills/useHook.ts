import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { FetchUserProfileSkillsResultI } from '@/src/networks/profile/types';
import { fetchUserSkills } from '@/src/networks/profile/userSkills';
import { OtherSkillsI } from './types';

export const useOtherSkills = () => {
  const { profileId } = useSelector(selectCurrentUser);
  const [localSkills, setLocalSkills] = useState<OtherSkillsI[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchOtherSkills = async () => {
      try {
        setLoading(true);
        const response = await fetchUserSkills(profileId, 'OTHER');
        const skillsSet = new Set();
        const deduplicatedSkills = response.filter((skill: FetchUserProfileSkillsResultI) => {
          if (skillsSet.has(skill.id)) {
            return false;
          }
          skillsSet.add(skill.id);
          return true;
        });
        setLocalSkills(deduplicatedSkills);
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            showToast({
              message: 'Failed to Fetch Skills',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOtherSkills();
  }, []);

  return {
    localSkills,
    loading,
  };
};
