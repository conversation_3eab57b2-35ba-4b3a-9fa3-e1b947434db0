import { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import {
  selectAboutProfileMaritimeSkills,
  selectAboutProfileMaritimeSkillsCount,
} from '@/src/redux/selectors/about';

export const useMaritimeSkills = () => {
  const maritimeSkills = useSelector(selectAboutProfileMaritimeSkills);
  const count = useSelector(selectAboutProfileMaritimeSkillsCount);

  const deduplicatedSkills = () => {
    if (!maritimeSkills || !Array.isArray(maritimeSkills)) return [];
    const skillsSet = new Set();
    return maritimeSkills.filter((skill) => {
      const identifier = skill.id || skill.name || JSON.stringify(skill);
      if (skillsSet.has(identifier)) {
        return false;
      }
      skillsSet.add(identifier);
      return true;
    });
  };

  const [localSkills, setLocalSkills] = useState(deduplicatedSkills);

  return {
    localSkills,
    count,
  };
};
