/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState, useRef } from 'react';
import { View, TextInput, Pressable, ActivityIndicator, Text, Keyboard } from 'react-native';
import Send from '@/src/assets/svgs/Send';

const MAX_ANSWER_LENGTH = 1000;

const AnswerInput = ({ onSubmit }: { onSubmit: (text: string) => void }) => {
  const [answer, setAnswer] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const canSubmit = answer.trim().length > 0 && !isSubmitting;
  const isNearLimit = answer.length > MAX_ANSWER_LENGTH * 0.8;

  const handleSend = () => {
    if (canSubmit) {
      setIsSubmitting(true);
      onSubmit(answer.trim());
      setAnswer('');
      setIsSubmitting(false);
      Keyboard.dismiss();
    }
  };

  return (
    <View className="bg-white border-t border-gray-200 p-4">
      <View className="flex-row items-start gap-3">
        <View className="flex-1">
          <View className="relative">
            <TextInput
              ref={inputRef}
              className="bg-gray-100 rounded-2xl text-black px-4 py-3 pr-12 text-base min-h-[44px] max-h-[120px]"
              placeholder="Write your answer..."
              placeholderTextColor="#9CA3AF"
              value={answer}
              onChangeText={setAnswer}
              maxLength={MAX_ANSWER_LENGTH}
              multiline
              autoCorrect
              editable={!isSubmitting}
              textAlignVertical="top"
            />
            <Pressable
              onPress={handleSend}
              disabled={!canSubmit}
              className={`absolute right-2 bottom-2 w-8 h-8 rounded-full items-center justify-center ${
                canSubmit ? 'bg-green-800' : 'bg-gray-300'
              }`}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Send width={1.6} height={1.6} color="white" strokeWidth={2} />
              )}
            </Pressable>
          </View>
          {isNearLimit && (
            <View className="flex-row justify-end items-center mt-2 px-2">
              <Text className="text-xs text-orange-500">
                {answer.length}/{MAX_ANSWER_LENGTH}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default AnswerInput;
