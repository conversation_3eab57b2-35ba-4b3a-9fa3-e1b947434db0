/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, FlatList, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ForumPost from '../../Forum/components/ForumPost';
import Answers from '../Answers';
import type { ForumAnswerProps } from '../Answers/types';

const AnswersList = ({ answers }: { answers: ForumAnswerProps[] }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  return (
    <View className="px-2 flex-1">
      <FlatList
        data={answers}
        keyExtractor={(item) => item.answerId}
        renderItem={({ item }) => (
          <Pressable onPress={() => navigation.navigate('ForumComments')}>
            <Answers answer={item} />
          </Pressable>
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingVertical: 20,
        }}
        ListHeaderComponent={
          <View className="bg-white">
            <ForumPost
              post={{
                postId: '1',
                profileId: 'user1',
                type: 'question',
                topics: [{ id: '1', label: 'Topic 1' }],
                heading: 'Sample Question Heading',
                solved: true,
                description:
                  'This is a sample question description to demonstrate the answers list.',
                previewIcons: ['link'],
                upVotes: 10,
                downVotes: 2,
                answers: answers.length,
                comments: 5,
                endTime: Date.now() + 1000 * 60 * 60 * 1,
                answerView: true,
              }}
            />
            <Text className="text-base font-medium text-black px-5 py-1">
              {answers.length} answers
            </Text>
          </View>
        }
      />
    </View>
  );
};

export default AnswersList;
