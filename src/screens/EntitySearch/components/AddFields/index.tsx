import { View } from 'react-native';
import OptionSelector from '@/src/components/OptionSelector';
import TextInput from '@/src/components/TextInput';
import ToggleSwitch from '@/src/components/Toggle';
import { NameTypePropsI } from './types';

const AddFields = ({ collection, onSelect, onChange, onToggle, hasFuelType }: NameTypePropsI) => {
  let options: { id: string; title: string }[] = [];

  switch (collection) {
    case 'entity':
      options = [
        { id: 'EDUCATION', title: 'Institution' },
        { id: 'COMPANY', title: 'Organisation' },
      ];
      break;
    case 'certificate-course':
      options = [
        { id: 'STATUTORY', title: 'Statutory' },
        { id: 'VALUE_ADDED', title: 'Value Added' },
      ];
      break;
    case 'skill':
      options = [
        { id: 'MARITIME', title: 'Maritime' },
        { id: 'OTHER', title: 'Other' },
      ];
      break;
    default:
      options = [];
      break;
  }
  return (
    <View>
      <OptionSelector options={options} onSelect={onSelect} />
      {collection === 'entity' && (
        <TextInput
          label="Website (optional)"
          placeholder="Enter website"
          onChangeText={onChange}
          className="mb-3"
        />
      )}
      {collection === 'equipmentCategory' && (
        <View className='mb-3'>
        <ToggleSwitch
          label="Has Fuel Type?"
          enabled={hasFuelType || false}
          onToggle={onToggle || (() => {})}
        />
        </View>
      )}
    </View>
  );
};

export default AddFields;
