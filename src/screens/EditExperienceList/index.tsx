import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditExperienceList from './components/EditExperienceList';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditExperienceList'>;

const EditExperienceListScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditExperienceList profileId={profileId as string} onBack={navigation.goBack} />
    </SafeArea>
  );
};

export default EditExperienceListScreen;
