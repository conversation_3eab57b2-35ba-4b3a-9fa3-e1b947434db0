/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import { useSelector } from 'react-redux';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import Close from '@/src/assets/svgs/Close';

const MAX_LENGTH = 3;

const qnaTopicsSelected = [] as string[];
const equipmentSelected = [] as string[];
const makeSelected = [] as string[];
const modelSelected = [] as string[];

const Filter = () => {
  const [liveEnabled, setLiveEnabled] = useState(false);
  const [recommendedEnabled, setRecommendedEnabled] = useState(false);
  const [qnaEnabled, setQnaEnabled] = useState(false);
  const [troubleshootingEnabled, setTroubleshootingEnabled] = useState(false);

  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const qnaTopicsSelection = useSelector(selectSelectionByKey('qnaTopics'));
  const equipmentSelection = useSelector(selectSelectionByKey('equipment'));
  const makeSelection = useSelector(selectSelectionByKey('make'));
  const modelSelection = useSelector(selectSelectionByKey('model'));

  if (
    qnaTopicsSelection &&
    qnaTopicsSelection.name &&
    !qnaTopicsSelected.includes(qnaTopicsSelection.name) &&
    qnaTopicsSelected.length < MAX_LENGTH
  ) {
    qnaTopicsSelected.push(qnaTopicsSelection.name);
  }
  if (
    equipmentSelection &&
    equipmentSelection.name &&
    !equipmentSelected.includes(equipmentSelection.name) &&
    equipmentSelected.length < MAX_LENGTH
  ) {
    equipmentSelected.push(equipmentSelection.name);
  }
  if (
    makeSelection &&
    makeSelection.name &&
    !makeSelected.includes(makeSelection.name) &&
    makeSelected.length < MAX_LENGTH
  ) {
    makeSelected.push(makeSelection.name);
  }
  if (
    modelSelection &&
    modelSelection.name &&
    !modelSelected.includes(modelSelection.name) &&
    modelSelected.length < MAX_LENGTH
  ) {
    modelSelected.push(modelSelection.name);
  }

  return (
    <View className="flex-1 p-4">
      <View className="py-4">
        <ToggleSwitch
          enabled={liveEnabled}
          onToggle={() => setLiveEnabled(!liveEnabled)}
          label="Live questions only"
        />
      </View>
      <View className="py-2">
        <ToggleSwitch
          enabled={recommendedEnabled}
          onToggle={() => setRecommendedEnabled(!recommendedEnabled)}
          label="Recommended questions only"
        />
      </View>
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-4">
        <Checkbox size={20} label="Questions I asked" />
        <Checkbox size={20} label="Questions I answered" />
        <Checkbox size={20} label="Questions from my communities" />
        <Checkbox size={20} label="Questions from all communities" />
      </View>
      <Text className="text-base font-medium text-black py-4">Posted when</Text>
      <View className="flex-row gap-4">
        <View className="flex-1">
          <DatePicker title="From" selectedDate="" onDateChange={() => {}} />
        </View>
        <View className="flex-1">
          <DatePicker title="To" selectedDate="" onDateChange={() => {}} />
        </View>
      </View>
      <Text className="text-base font-medium text-black py-4">Connections</Text>
      <View className="flex-row gap-8">
        <View>
          <Checkbox size={20} label="1st" />
        </View>
        <View>
          <Checkbox size={20} label="2nd" />
        </View>
        <View>
          <Checkbox size={20} label="3rd" />
        </View>
      </View>
      <EntitySearch
        titleClassName="text-base text-black py-2"
        title="Department"
        selectionKey="department"
        placeholder="Select department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-2">
        <RadioButton
          selected={qnaEnabled}
          onPress={() => {
            setQnaEnabled(!qnaEnabled);
            setTroubleshootingEnabled(false);
          }}
          label="QnA"
          inFilter={true}
        />
        {qnaEnabled && (
          <View>
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Topics"
              selectionKey="qnaTopics"
              placeholder="Select topic/s"
              data={qnaTopicsSelection ? qnaTopicsSelection.name : ''}
            />
            {qnaTopicsSelected.length > 0 && (
              <View className="flex-row gap-2 flex-wrap">
                {qnaTopicsSelected.map((topic) => (
                  <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                    <Text key={topic} className="text-sm text-black">
                      {topic}
                    </Text>
                    <Pressable onPress={() => {}}>
                      {' '}
                      <Close width={1.8} height={1.8} />{' '}
                    </Pressable>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
        <RadioButton
          selected={troubleshootingEnabled}
          onPress={() => {
            setTroubleshootingEnabled(!troubleshootingEnabled);
            setQnaEnabled(false);
          }}
          label="Troubleshooting"
          inFilter={true}
        />
        {troubleshootingEnabled && (
          <View className="flex-col gap-2">
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Equipment"
              selectionKey="equipment"
              placeholder="Select equipment/s"
              data={equipmentSelection ? equipmentSelection.name : ''}
            />
            {equipmentSelected.length > 0 && (
              <View className="flex-row gap-2 flex-wrap">
                {equipmentSelected.map((equip) => (
                  <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                    <Text key={equip} className="text-sm text-black">
                      {equip}
                    </Text>
                    <Pressable onPress={() => {}}>
                      {' '}
                      <Close width={1.8} height={1.8} />{' '}
                    </Pressable>
                  </View>
                ))}
              </View>
            )}
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Make"
              selectionKey="make"
              placeholder="Select make/s"
              data={makeSelection ? makeSelection.name : ''}
            />
            {makeSelected.length > 0 && (
              <View className="flex-row gap-2 flex-wrap">
                {makeSelected.map((make) => (
                  <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                    <Text key={make} className="text-sm text-black">
                      {make}
                    </Text>
                    <Pressable onPress={() => {}}>
                      {' '}
                      <Close width={1.8} height={1.8} />{' '}
                    </Pressable>
                  </View>
                ))}
              </View>
            )}
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Model"
              selectionKey="model"
              placeholder="Select model/s"
              data={modelSelection ? modelSelection.name : ''}
            />
            {modelSelected.length > 0 && (
              <View className="flex-row gap-2 flex-wrap">
                {modelSelected.map((model) => (
                  <View className="flex-row items-center gap-1 bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                    <Text key={model} className="text-sm text-black">
                      {model}
                    </Text>
                    <Pressable onPress={() => {}}>
                      {' '}
                      <Close width={1.8} height={1.8} />{' '}
                    </Pressable>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

export default Filter;
