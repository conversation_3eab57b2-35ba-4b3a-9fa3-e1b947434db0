/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Filter from './Filter';

const ForumFilterScreen = () => {
  const navigation = useNavigation();
  return (
    <SafeArea>
      <View className="flex-row items-center justify-between px-4">
        <View className="flex-row items-center">
          <BackButton onBack={() => navigation.goBack()} label="" />
          <Text className="text-xl font-medium text-black">Filters</Text>
        </View>
        <Pressable className="px-4 items-center">
          <Text className="text-xl font-medium text-[#448600]">Apply</Text>
        </Pressable>
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Filter />
      </ScrollView>
    </SafeArea>
  );
};

export default ForumFilterScreen;
