import { useEffect, useState, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { saveUsernameAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import type { AppStackParamListI } from '@/src/navigation/types';
import { checkUsernameAPI } from '@/src/networks/profile/username';
import type { SetUsernameFormDataI, UseSetUsernameFormI } from './types';

const useSetUsername = (email: string): UseSetUsernameFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState(true);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);

  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckedUsernameRef = useRef<string>('');

  const defaultValues = {
    userName: '',
    email,
  };

  const methods = useForm<SetUsernameFormDataI>({
    mode: 'onChange',
    defaultValues,
  });

  const { watch, setError, clearErrors } = methods;
  const userName = watch('userName').trim();

  useEffect(() => {
    if (email) {
      methods.setValue('email', email);
    }
  }, [email, methods]);

  const checkUsername = async (username: string) => {
    if (!username || username.length < 4) {
      setIsUsernameAvailable(true);
      clearErrors('userName');
      setIsCheckingUsername(false);
      lastCheckedUsernameRef.current = '';
      return;
    }

    if (username === lastCheckedUsernameRef.current) {
      return;
    }

    setIsCheckingUsername(true);
    lastCheckedUsernameRef.current = username;

    try {
      await checkUsernameAPI({ username });
      setIsUsernameAvailable(true);
      clearErrors('userName');
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          if (error instanceof APIResError && error.status === 409) {
            setIsUsernameAvailable(false);
            setError('userName', {
              message: 'Username is already taken',
            });
            return;
          }
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsCheckingUsername(false);
    }
  };

  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    if (userName.length < 4) {
      setIsUsernameAvailable(true);
      clearErrors('userName');
      setIsCheckingUsername(false);
      lastCheckedUsernameRef.current = '';
      return;
    }

    debounceTimeoutRef.current = setTimeout(() => {
      checkUsername(userName);
    }, 500);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [userName]);

  const onSubmit = async (data: SetUsernameFormDataI) => {
    if (data.userName.trim().length < 4) {
      setError('userName', {
        message: 'Username must be at least 4 characters',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        saveUsernameAsync({
          username: data.userName.trim(),
        }),
      ).unwrap();
      navigation.navigate('AddUserDetailScreen');
    } catch (error) {
      setIsSubmitting(false);
      handleError(error);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isUsernameAvailable,
    isCheckingUsername,
  };
};

export default useSetUsername;
