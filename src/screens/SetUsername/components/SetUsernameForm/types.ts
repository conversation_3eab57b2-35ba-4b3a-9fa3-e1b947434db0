/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { UseFormReturn } from 'react-hook-form';

export interface SetUsernameFormDataI {
  userName: string;
  email: string;
}

export interface UseSetUsernameFormI {
  methods: UseFormReturn<SetUsernameFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: SetUsernameFormDataI) => Promise<void>;
  isUsernameAvailable: boolean;
  isCheckingUsername: boolean;
}
