import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Controller } from 'react-hook-form';
// import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ExperienceDesignationI } from '@/src/redux/slices/experience/types';
import { navigate } from '@/src/utilities/navigation';
import { ProfileStackParamsListI } from '@/src/navigation/types';
// import { ProfileStackParamsListI } from '@/src/navigation/types';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
// import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
// import { toMonthYear } from '../utils';
import { EditExperienceItemPropsI, FieldTypeI } from './types';
import useEditExperienceItem from './useHook';

const ShipItem = ({
  field,
  onAddEdit,
  deleteShip,
  isDeleting,
  company,
}: {
  field: FieldTypeI;
  onAddEdit: (field: FieldTypeI, shipId?: string | undefined) => void;
  deleteShip: (field: FieldTypeI, shipId: string) => void;
  isDeleting: boolean;
  company: SearchResultI;
}) => {
  const [shipDeleteId, setShipDeleteId] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  return (
    <>
      <View className="px-4 py-2 bg-[#F5F5F5] rounded-lg">
        <View className="flex-row items-center justify-between">
          <TextView subtitle="Ship details" subtitleClassName="font-bold text-black" />
          {company?.id && field?.designation?.id && field?.fromDate && field?.toDate && (
            <Pressable onPress={() => onAddEdit(field)}>
              <AddItem />
            </Pressable>
          )}
        </View>
        {(field.ships ?? []).length === 0 && (
          <NotFound
            imageStyle={{ width: '50%', height: 100 }}
            fullScreen={false}
            className="py-5"
          />
        )}
        {(field.ships ?? []).map((ship) => {
          return (
            <View className="py-3" key={ship.id}>
              <TextView subtitle={`${ship.name}`} subtitleClassName="font-bold" />
              <View className="flex-row items-center justify-between">
                <TextView
                  subtitle={`${toMonthYear(ship.fromDate)} - ${ship.toDate != null ? toMonthYear(ship.toDate) : 'Present'}`}
                />
                <View className="flex-row items-center gap-4">
                  <Pressable onPress={() => onAddEdit(field, ship.id)}>
                    <EditPencil width={2.3} height={2.3} />
                  </Pressable>
                  <Pressable
                    onPress={() => {
                      setShipDeleteId(ship.id);
                      setIsVisible(true);
                    }}
                  >
                    <DeleteIcon width={2.3} height={2.3} />
                  </Pressable>
                </View>
              </View>
            </View>
          );
        })}
      </View>
      <CustomModal
        isVisible={isVisible}
        onCancel={() => {
          setShipDeleteId(null);
          setIsVisible(false);
        }}
        title="Are you sure you want to delete this ship?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          if (shipDeleteId) {
            deleteShip(field, shipDeleteId);
            setIsVisible(false);
          }
        }}
        isConfirming={isDeleting}
      />
    </>
  );
};

const EditExperienceItem = ({ onBack, profileId, experienceId }: EditExperienceItemPropsI) => {
  const {
    isSubmitting,
    loading,
    designations,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations,
    setLocalDesignations,
    handleDateChange,
    localEntity,
    handleSubmit,
  } = useEditExperienceItem(profileId, experienceId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit Experience" />
          <Pressable onPress={handleSubmit} disabled={isSubmitting}>
            <Text className="text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <EntitySearch
          title="Company"
          placeholder="Enter Company ..."
          selectionKey="entity/organisation"
          data={localEntity?.name}
        />
        <Pressable onPress={handleAddDesignation}>
          <View className="flex-row justify-between mt-4 items-center">
            <TextView subtitle="Designation Details" subtitleClassName="font-bold text-black" />
            <AddItem />
          </View>
        </Pressable>
        {localDesignations.map((item, index) => {
          return (
            <View className="" key={index}>
              <EntitySearch
                title={`Designation ${index + 1}`}
                placeholder="Enter Designation"
                selectionKey={`designation-${index}`}
                data={
                  designations.find((d) => Number(d.indexKey) === index)?.name ??
                  localDesignations?.[index]?.designation?.name
                }
              />

              <View className="flex-row mb-6 mt-4">
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="From"
                    selectedDate={item.fromDate || ''}
                    onDateChange={(date) => handleDateChange(index, 'fromDate', date)}
                    showMonthYear={true}
                  />
                </View>
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="To"
                    selectedDate={item.toDate || ''}
                    onDateChange={(date) => handleDateChange(index, 'toDate', date)}
                    showMonthYear={true}
                  />
                </View>
              </View>

              <ShipItem
                field={item}
                onAddEdit={(field, shipId) => handleAddEditShip(field, shipId)}
                deleteShip={(field, shipId) => handleDeleteShip(field, shipId)}
                isDeleting={isDeleting}
                company={localEntity}
              />

              <Pressable
                onPress={() => {
                  setLocalDesignations((prev) => prev.filter((_, i) => i !== index));
                }}
                className="flex-row items-center self-end mb-4 gap-2 mt-2"
              >
                <DeleteIcon color="red" width={1.75} height={1.75} />
                <Text className="text-red-500">Remove</Text>
              </Pressable>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

export default EditExperienceItem;
