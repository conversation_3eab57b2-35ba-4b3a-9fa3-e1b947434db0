import type React from 'react';
import { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import { useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import CameraIcon from '@/src/assets/svgs/Camera';
import type { AvatarPickerProps } from './types';

const AvatarPicker: React.FC<AvatarPickerProps> = ({
  value,
  onChange,
  onDelete,
  size = 100,
  editable = true,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [shouldShowDeleteModal, setShouldShowDeleteModal] = useState(false);
  const currentUser = useSelector(selectCurrentUser);

  const showImagePicker = () => {
    if (!editable) return;
    setIsModalVisible(true);
  };

  const hideModal = () => {
    setIsModalVisible(false);
  };

  const openGallery = () => {
    setTimeout(() => {
      ImagePicker.openPicker({
        width: 400,
        height: 400,
        cropping: true,
        cropperCircleOverlay: true,
        compressImageMaxWidth: 400,
        compressImageMaxHeight: 400,
        compressImageQuality: 0.8,
        includeBase64: false,
      })
        .then((image) => {
          onChange(image);
        })
        .catch((error) => {
          if (error.code !== 'E_PICKER_CANCELLED') {
            console.error('Gallery error:', error);
          }
        });
    }, 500);
    hideModal();
  };

  const handleDelete = () => {
    setShouldShowDeleteModal(true);
    hideModal();
  };

  const handleModalHide = () => {
    if (shouldShowDeleteModal) {
      setShouldShowDeleteModal(false);
      setIsDeleteModalVisible(true);
    }
  };

  const confirmDelete = () => {
    setIsDeleteModalVisible(false);
    onDelete?.();
  };

  let avatarUri = null;
  if (value) {
    if (typeof value === 'string') {
      avatarUri = value;
    } else {
      avatarUri = value.path;
    }
  }

  const hasAvatar = Boolean(value);

  return (
    <>
      <View className="items-center justify-center my-4">
        <Pressable onPress={showImagePicker} className="relative" disabled={!editable}>
          <UserAvatar
            avatarUri={avatarUri}
            name={currentUser.fullName}
            width={size}
            height={size}
          />

          {editable && (
            <View className="absolute bottom-0 right-0 bg-white p-2 rounded-full shadow-md border border-gray-200">
              <CameraIcon width={2.2} height={2.2} stroke="#448600" />
            </View>
          )}
        </Pressable>

        {editable && (
          <Text className="text-sm text-gray-500 mt-2">Tap to change profile photo</Text>
        )}
      </View>

      <BottomSheet
        visible={isModalVisible}
        onClose={hideModal}
        height={hasAvatar ? 280 : 220}
        onModalHide={handleModalHide}
      >
        <View className="py-4">
          <Text className="text-lg font-medium text-gray-900 text-center mb-4">Profile Photo</Text>

          <View className="gap-2">
            <Pressable onPress={openGallery} className="flex-row items-center p-4 rounded-lg">
              <Text className="text-base text-gray-900">Choose from Gallery</Text>
            </Pressable>

            {hasAvatar && (
              <Pressable onPress={handleDelete} className="flex-row items-center p-4 rounded-lg">
                <Text className="text-base text-red-600">Remove Photo</Text>
              </Pressable>
            )}
          </View>
        </View>
      </BottomSheet>

      <CustomModal
        isVisible={isDeleteModalVisible}
        title="Delete Profile Photo"
        description="Are you sure you want to remove your profile photo?"
        cancelText="Cancel"
        confirmText="Delete"
        onConfirm={confirmDelete}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmButtonVariant="danger"
      />
    </>
  );
};

export default AvatarPicker;
