import { z } from 'zod';

export const createQuestionSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must not exceed 200 characters')
    .trim(),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(2000, 'Description must not exceed 2000 characters')
    .trim(),
});

export type QuestionFormData = z.infer<typeof createQuestionSchema>;
