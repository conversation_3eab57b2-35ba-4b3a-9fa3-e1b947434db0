import { useEffect } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '../utils';
import { EditEducationItemPropsI } from './types';
import { useEditEducationItem } from './useHook';

const EditEducationItem = ({ onBack, profileId, educationId }: EditEducationItemPropsI) => {
  const {
    methods,
    isSubmitting,
    onSubmit,
    localSkills,
    setLocalSkills,
    loading,
    isPresent,
    handlePresentCheckbox,
    clearFields,
    isSubmitted,
    setIsSubmitted,
  } = useEditEducationItem(profileId, educationId);

  const {
    control,
    handleSubmit,
    watch,
    /*formState: { errors }*/
  } = methods;

  //   const education = useSelector((state: RootState) => state.education.education);
  const instituteSelection = useSelector(selectSelectionByKey('entity'));
  const degreeSelection = useSelector(selectSelectionByKey('degree'));

  useEffect(() => {
    if (instituteSelection) {
      methods.setValue('institution', instituteSelection);
    }
    if (degreeSelection) {
      methods.setValue('degree', degreeSelection);
    }

    return () => {
      clearFields();
    };
  }, [degreeSelection, instituteSelection, methods]);

  const FromDate = watch('fromDate');
  const ToDate = watch('toDate');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit Education" />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting}
          >
            <Text className="text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <EntitySearch
          title="Institution"
          placeholder="Enter Institution ..."
          selectionKey="entity/institution"
          data={instituteSelection ? instituteSelection.name : methods.watch('institution').name}
          error={
            isSubmitted && !instituteSelection && !methods.watch('institution')?.id
              ? `Institution is required`
              : undefined
          }
        />
        <EntitySearch
          title="Degree"
          placeholder="Enter Degree ..."
          selectionKey="degree"
          data={degreeSelection ? degreeSelection.name : methods.watch('degree').name}
          error={
            isSubmitted && !degreeSelection && !methods.watch('degree')?.id
              ? `Degree is required`
              : undefined
          }
        />

        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="fromDate"
              rules={{
                required: 'From date is required',
                validate: (value) => validateDate(value, ToDate),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="From"
                    selectedDate={FromDate}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="toDate"
              rules={{
                required: isPresent ? false : 'To date is required',
                validate: (value) => validateDate(FromDate, value),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="To"
                    selectedDate={ToDate!}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    disabled={isPresent}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
            <Checkbox
              label="Unlimited"
              className="pt-3"
              labelClassName="text-base text-sm"
              onValueChange={handlePresentCheckbox}
              checked={isPresent}
            />
          </View>
        </View>

        <View className="mb-6">
          <ChipInput
            title="Skills"
            placeholder="Add a skill"
            chips={localSkills}
            onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default EditEducationItem;
