/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { emailRegex, strongPasswordRegex } from '@/src/consts/regEx';
import { CreateAccountPropsI } from './types';
import useCreateAccount from './useHook';

const CreateAccountForm = ({ onSignIn }: CreateAccountPropsI) => {
  const { methods, isSubmitting, onSubmit } = useCreateAccount();

  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid, errors },
  } = methods;

  const password = watch('password');

  return (
    <View className="px-5">
      <View className="my-8">
        <TextView
          title="Create account"
          subtitle="Enter your email ID and password to create an account"
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="email"
          rules={{
            required: 'Email is required',
            pattern: {
              value: emailRegex,
              message: 'Invalid email address',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Email ID"
              placeholder="Enter email ID"
              value={value}
              onChangeText={onChange}
              error={errors.email?.message}
              editable
              keyboardType="email-address"
              autoCapitalize="none"
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="password"
          rules={{
            required: 'Password is required',
            pattern: {
              value: strongPasswordRegex,
              message:
                'Password must be at least 8 characters, include an uppercase letter, a lowercase letter, a number, and a special character.',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="New password"
              placeholder="Enter new password"
              value={value}
              onChangeText={onChange}
              type="password"
              error={errors.password?.message}
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="confirmPassword"
          rules={{
            required: 'Please confirm your password',
            validate: (value) => value === password || 'Passwords do not match',
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Confirm new password"
              placeholder="Confirm new password"
              value={value}
              onChangeText={onChange}
              type="password"
              error={errors.confirmPassword?.message}
            />
          )}
        />
      </View>
      <View className="mt-8 space-y-4">
        <Button
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid || isSubmitting}
          label="Create account"
          variant={isValid ? 'primary' : 'tertiary'}
          loading={isSubmitting}
          labelClassName="text-base font-medium"
        />
      </View>
      <View className="flex-row justify-center items-center mt-8">
        <Text className="text-sm text-[#333333]">Already have an account? </Text>
        <Pressable onPress={onSignIn}>
          <Text className="text-sm text-[#448600] font-medium font-inter-medium">Sign in</Text>
        </Pressable>
      </View>
    </View>
  );
};

export default CreateAccountForm;
