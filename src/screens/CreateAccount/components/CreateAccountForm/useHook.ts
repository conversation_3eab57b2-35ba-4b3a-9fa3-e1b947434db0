/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { AuthStackParamListI } from '@/src/navigation/types';
import useStorage from '@/src/hooks/storage';
import { sendOTPForEmailVerificationAPI } from '@/src/networks/auth/email';
import { registerAPI } from '@/src/networks/auth/register';
import { CreateAccountFormDataI, UseCreateAccountFormI } from './types';

const useCreateAccount = (): UseCreateAccountFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<AuthStackParamListI>>();
  const { setStorage } = useStorage();

  const methods = useForm<CreateAccountFormDataI>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const { setError } = methods;

  const onSubmit = async (data: CreateAccountFormDataI) => {
    try {
      setIsSubmitting(true);

      if (data.password !== data.confirmPassword) {
        setError('confirmPassword', {
          message: 'Passwords do not match',
        });
        return;
      }

      const result = await registerAPI({
        type: 'EMAIL_PASSWORD',
        email: data.email.trim(),
        password: data.password,
        confirmPassword: data.confirmPassword,
      });

      await setStorage('token', result.token);

      try {
        await sendOTPForEmailVerificationAPI({
          profileId: result.profileId,
        });
        navigation.navigate('VerifyEmail', {
          profileId: result.profileId,
          email: data.email.trim(),
        });
      } catch (otpError) {
        showToast({
          type: 'error',
          message: 'Account Created',
          description:
            'Account created but failed to send verification email. Please try again later.',
        });
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          if (error instanceof APIResError) {
            switch (error.code) {
              case 'AUTH005':
                setError('email', {
                  message: 'Email not found. Please check your email address.',
                });
                break;
              case 'AUTH007':
                showToast({
                  type: 'error',
                  message: 'Invalid Data',
                  description: 'Please check your input and try again',
                });
                break;
              case 'AUTH008':
                showToast({
                  type: 'error',
                  message: 'Registration Method Not Supported',
                  description: 'Please use email and password registration',
                });
                break;
              case 'AUTH009':
                setError('email', {
                  message: 'Email already exists. Please use a different email address.',
                });
                break;
              default:
                showToast({
                  type: 'error',
                  message: 'Registration Failed',
                  description: 'Please check your information and try again',
                });
            }
          } else {
            showToast({
              type: 'error',
              message: 'Registration Failed',
              description: 'Please try again later',
            });
          }
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Server Error',
            description: 'Please try again later',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
  };
};

export default useCreateAccount;
