import type { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';

export const forumPostsDummy: ForumPostProps[] = [
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Engine' },
      { id: '2', label: 'Maintenance' },
    ],
    heading: 'How to fix engine overheating?',
    solved: false,
    description: 'My engine overheats after 2 hours of operation. Any suggestions?',
    previewIcons: ['photo', 'pdf'],
    upVotes: 12,
    downVotes: 1,
    answers: 4,
    comments: 2,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Engine' },
      { id: '2', label: 'Maintenance' },
    ],
    heading: 'How to fix engine overheating?',
    solved: false,
    description: 'My engine overheats after 2 hours of operation. Any suggestions?',
    previewIcons: ['photo', 'pdf'],
    upVotes: 12,
    downVotes: 1,
    answers: 4,
    comments: 2,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Engine' },
      { id: '2', label: 'Maintenance' },
    ],
    heading: 'How to fix engine overheating?',
    solved: false,
    description: 'My engine overheats after 2 hours of operation. Any suggestions?',
    previewIcons: ['photo', 'pdf'],
    upVotes: 12,
    downVotes: 1,
    answers: 4,
    comments: 2,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Engine' },
      { id: '2', label: 'Maintenance' },
    ],
    heading: 'How to fix engine overheating?',
    solved: false,
    description: 'My engine overheats after 2 hours of operation. Any suggestions?',
    previewIcons: ['photo', 'pdf'],
    upVotes: 12,
    downVotes: 1,
    answers: 4,
    comments: 2,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '1',
    profileId: 'user1',
    type: 'question',
    topics: [
      { id: '1', label: 'Engine' },
      { id: '2', label: 'Maintenance' },
    ],
    heading: 'How to fix engine overheating?',
    solved: false,
    description: 'My engine overheats after 2 hours of operation. Any suggestions?',
    previewIcons: ['photo', 'pdf'],
    upVotes: 12,
    downVotes: 1,
    answers: 4,
    comments: 2,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 5,
    answerView: false,
  },
  {
    postId: '2',
    profileId: 'user2',
    type: 'troubleshooting',
    equipment: [
      { id: '1', label: 'Pump' },
      { id: '2', label: 'BrandX' },
      { id: '3', label: 'ModelY' },
    ],
    heading: 'Pump not starting',
    solved: true,
    description: 'The main pump does not start even after resetting the breaker.',
    previewIcons: ['link'],
    upVotes: 7,
    downVotes: 0,
    answers: 2,
    comments: 1,
    community: 'Beta Community',
    endTime: Date.now() + 1000 * 60 * 60 * 2,
    answerView: false,
  },
  {
    postId: '3',
    profileId: 'user3',
    type: 'question',
    topics: [{ id: '3', label: 'Navigation' }],
    heading: 'Best practices for night navigation?',
    solved: false,
    description: 'Looking for tips and best practices for safe night navigation.',
    previewIcons: ['video'],
    upVotes: 5,
    downVotes: 2,
    answers: 1,
    comments: 0,
    community: 'Alpha Community',
    endTime: Date.now() + 1000 * 60 * 60 * 10,
    answerView: false,
  },
  {
    postId: '4',
    profileId: 'user4',
    type: 'question',
    topics: [{ id: '4', label: 'Safety' }],
    heading: 'How to conduct safety drills?',
    solved: true,
    description: 'What are the steps to conduct a fire safety drill onboard?',
    previewIcons: ['excel', 'pdf'],
    upVotes: 9,
    downVotes: 1,
    answers: 3,
    comments: 2,
    community: 'Gamma Community',
    endTime: Date.now() + 1000 * 60 * 60 * 8,
    answerView: false,
  },
];

export const communitiesDummy: string[] = [
  'Alpha Community',
  'Beta Community',
  'Gamma Community',
  'Delta Community',
];
