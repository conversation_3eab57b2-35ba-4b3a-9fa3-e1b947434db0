/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import NotFound from '@/src/components/NotFound';
import ForumPost from '../../Forum/components/ForumPost';
import type { ForumPostProps } from '../../Forum/components/ForumPost/types';
import { ForumSearchType } from '../SearchBox/types';
import type { SearchResultsProps } from './types';

const SearchResults: React.FC<SearchResultsProps & ForumSearchType> = ({
  loading,
  searchResults,
  onRefresh,
  refreshing,
  activeTab,
  searchType = 'general',
}) => {
  const data = searchResults?.data;

  if (loading && (!data || data.length === 0)) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <View className="flex-1 justify-center">
        <NotFound title="No Results Found" subtitle="Try searching for a different keyword" />
      </View>
    );
  }

  return (
    <View className="flex-1">
      {activeTab === 'posts' ? (
        <FlatList
          data={data as ForumPostProps[]}
          keyExtractor={(item) => (item as ForumPostProps).postId}
          renderItem={({ item }) => <ForumPost post={item as ForumPostProps} />}
          refreshControl={
            <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={{ flexGrow: 1 }}
        />
      ) : (
        <FlatList
          data={data as string[]}
          keyExtractor={(item, index) => item + index}
          renderItem={({ item }) => (
            <View className="bg-white border-b border-gray-200 px-4 py-4">
              <Text className="text-base text-black">{item}</Text>
            </View>
          )}
          refreshControl={
            <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: 'white',
            paddingVertical: 20,
          }}
        />
      )}
    </View>
  );
};

export default SearchResults;
