/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';

export type ForumSearchCategory = 'posts' | 'communities';

export type ForumSearchCategoryTabs = {
  id: ForumSearchCategory;
  label: string;
};

export type ForumSearchResponse = {
  data: ForumPostProps[] | string[];
  total: number;
};

export interface SearchResultsProps {
  searchResults: ForumSearchResponse;
  loading: boolean;
  refreshing: boolean;
  activeTab: ForumSearchCategory;
  onRefresh: () => void;
}
