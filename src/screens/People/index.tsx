import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import SafeArea from '@/src/components/SafeArea';
import Search from '@/src/assets/svgs/Search';
import Tick from '@/src/assets/svgs/Tick';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';

interface User {
  id: number;
  name: string;
  title: string;
  avatar: string;
}

const PeopleScreen = () => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  const allUsers: User[] = [
    {
      id: 2,
      name: '<PERSON>',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
    },
    {
      id: 3,
      name: '<PERSON>',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
    },
    {
      id: 4,
      name: 'Mitchell Samal',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
    },
    {
      id: 5,
      name: 'Hazel Nutt',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-*************-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
    },
    {
      id: 6,
      name: 'Ole Ullrich',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-*************-af0119f7cbe7?w=60&h=60&fit=crop&crop=face',
    },
    {
      id: 7,
      name: 'Patricia Banks',
      title: 'Second Engineer at Seashore Corporations',
      avatar:
        'https://images.unsplash.com/photo-*************-8f129e1688ce?w=60&h=60&fit=crop&crop=face',
    },
  ];

  const filteredUsers: User[] = allUsers.filter((user) =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const toggleUserSelection = (user: User): void => {
    setSelectedUsers((prev) => {
      const isSelected = prev.some((u) => u.id === user.id);
      if (isSelected) {
        return prev.filter((u) => u.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  const isUserSelected = (userId: number): boolean => {
    return selectedUsers.some((u) => u.id === userId);
  };

  return (
    <SafeArea>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View className="flex-1 bg-white">
          <ScrollView className="flex-1 px-4">
            <CreateCommunityHeader currentPage={3} onNext={() => {}} buttonTitle="Create" />
            <Text className="text-xl font-medium my-4">Add people</Text>
            <View className="relative mb-6">
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Sam"
                className="bg-gray-100 rounded-lg px-4 py-3 pl-10 text-base"
                placeholderTextColor="#9CA3AF"
              />
              <View className="absolute left-3 top-3">
                <Search width={1.75} height={1.75} />
              </View>
            </View>

            {selectedUsers.length > 0 && (
              <View className="mb-6">
                <Text className="text-gray-500 text-sm mb-3">Selected members</Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  className="flex-row"
                  contentContainerStyle={{ paddingRight: 16 }}
                >
                  {selectedUsers.slice(0, 5).map((user, index) => (
                    <View key={user.id} className={`items-center ${index > 0 ? 'ml-4' : ''}`}>
                      <View className="relative">
                        <Image source={{ uri: user.avatar }} className="w-12 h-12 rounded-full" />
                        <View className="absolute -bottom-1 -right-1 w-6 h-6 bg-primaryGreen rounded items-center justify-center">
                          <Tick width={1.5} height={1.5} color="white" />
                        </View>
                      </View>
                      <Text
                        className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                        numberOfLines={1}
                      >
                        {user.name.split(' ')[0]}
                      </Text>
                    </View>
                  ))}
                  {selectedUsers.length > 5 && (
                    <View className="items-center ml-4">
                      <View className="relative">
                        <View className="w-12 h-12 rounded-full bg-gray-200 items-center justify-center">
                          <Text className="text-gray-600 font-medium">
                            +{selectedUsers.length - 5}
                          </Text>
                        </View>
                      </View>
                      <Text
                        className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                        numberOfLines={1}
                      >
                        More
                      </Text>
                    </View>
                  )}
                </ScrollView>
              </View>
            )}

            <View className="pb-6">
              {filteredUsers.map((user) => (
                <TouchableOpacity
                  key={user.id}
                  onPress={() => toggleUserSelection(user)}
                  className="flex-row items-center justify-between py-3"
                >
                  <View className="flex-row items-center flex-1">
                    <Image source={{ uri: user.avatar }} className="w-12 h-12 rounded-full" />
                    <View className="ml-3 flex-1">
                      <Text className="text-base font-medium text-gray-900">{user.name}</Text>
                      <Text className="text-sm text-gray-500 mt-1">{user.title}</Text>
                    </View>
                  </View>
                  <View
                    className={`w-6 h-6 rounded border-2 items-center justify-center ${
                      isUserSelected(user.id)
                        ? 'bg-primaryGreen border-primaryGreen'
                        : 'border-gray-300'
                    }`}
                  >
                    {isUserSelected(user.id) && <Tick width={1.5} height={1.5} color="white" />}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default PeopleScreen;
