/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ReactNode } from 'react';
import { DimensionValue } from 'react-native';

export interface BottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onModalHide: () => void;
  children: ReactNode;
  height?: DimensionValue;
  backdropOpacity?: number;
}
