import type React from 'react';
import { useState, useEffect } from 'react';
import { View, Dimensions, type DimensionValue, Keyboard } from 'react-native';
import Modal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { BottomSheetProps } from './types';

const BottomSheet: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  onModalHide,
  children,
  height,
}) => {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get('window').height;
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidHideListener?.remove();
      keyboardDidShowListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (visible) {
      if (isKeyboardVisible) {
        setShouldShow(false);
        const timer = setTimeout(() => {
          Keyboard.dismiss();
        }, 100);
        return () => clearTimeout(timer);
      } else {
        const timer = setTimeout(() => {
          setShouldShow(true);
        }, 150);
        return () => clearTimeout(timer);
      }
    } else {
      setShouldShow(false);
    }
  }, [visible, isKeyboardVisible]);

  const getHeight = (): DimensionValue => {
    if (!height) return screenHeight * 0.5;
    if (typeof height === 'string') {
      const percentage = Number.parseInt(height.replace('%', ''));
      return (screenHeight * percentage) / 100;
    }
    return height;
  };

  const handleModalHide = () => {
    setShouldShow(false);
    onModalHide?.();
  };

  return (
    <View>
      <Modal
        isVisible={shouldShow && !isKeyboardVisible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        onModalHide={handleModalHide}
        style={{ margin: 0, justifyContent: 'flex-end' }}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        backdropOpacity={0.6}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        useNativeDriverForBackdrop={true}
        statusBarTranslucent
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <View
          className="bg-white rounded-t-2xl py-3"
          style={{
            height: getHeight(),
            paddingBottom: insets.bottom,
          }}
        >
          <View className="flex-1 px-4">{children}</View>
        </View>
      </Modal>
    </View>
  );
};

export default BottomSheet;
