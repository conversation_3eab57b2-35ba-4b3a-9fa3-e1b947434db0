/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Pressable, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { navigate } from '@/src/utilities/navigation';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import { EntitySearchProps } from './types';

const EntitySearch = ({
  title,
  placeholder,
  data,
  selectionKey,
  multipleSelection = false,
  className,
  titleClassName,
  inputClassName,
  error,
  editable = true,
  searchWithoutInput = false,
}: EntitySearchProps) => {
  const handlePress = () => {
    navigate('SearchScreen', {
      title,
      placeholder,
      selectionKey,
      multipleSelection,
      searchWithoutInput,
    });
  };

  const displayText = data
    ? typeof data === 'object' && 'title' in data
      ? data.title
      : data
    : placeholder;

  return (
    <View className={twMerge('my-3', className)}>
      <Text className={twMerge('font-medium text-sm text-gray-700 mb-2', titleClassName)}>
        {title}
      </Text>

      <Pressable
        onPress={handlePress}
        className={twMerge(
          'relative w-full h-14 flex-row items-center justify-between px-4 border rounded-xl active:bg-gray-50',
          error ? 'border-red-500' : 'border-gray-300',
          inputClassName,
        )}
        android_ripple={{ color: 'rgba(0, 0, 0, 0.05)' }}
        disabled={!editable}
      >
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          className={twMerge('flex-1 pr-8', data ? 'text-gray-700' : 'text-gray-500')}
        >
          {displayText?.toString() || placeholder}
        </Text>

        {editable && (
          <View className="absolute right-1">
            <ChevronRight stroke="#333" strokeWidth={1} />
          </View>
        )}
      </Pressable>

      {error && <Text className="text-red-500 text-xs mt-1">{error}</Text>}
    </View>
  );
};

export default EntitySearch;
