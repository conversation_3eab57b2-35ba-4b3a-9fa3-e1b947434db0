import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import Close from '@/src/assets/svgs/Close';
import { ChipProps } from './types';

const Chip: React.FC<ChipProps> = ({
  label,
  onPress,
  className,
  labelClassName,
  prefixIcon,
  suffixIcon,
  removable = false,
  onRemove,
}) => (
  <Pressable
    onPress={onPress}
    className={twMerge(
      'bg-gray-100 border border-gray-200 rounded-full px-4 py-2 flex-row items-center justify-between',
      className,
    )}
  >
    <View className="flex-row items-center gap-2">
      {prefixIcon && <View>{prefixIcon}</View>}
      <Text
        className={twMerge('text-black text-base', labelClassName)}
        numberOfLines={1}
        ellipsizeMode="tail"
        style={{ maxWidth: 250 }}
      >
        {label}
      </Text>
    </View>
    {(removable || suffixIcon) && (
      <Pressable onPress={onRemove} className="ml-2">
        {suffixIcon || <Close width={2} height={2} />}
      </Pressable>
    )}
  </Pressable>
);

export default Chip;
