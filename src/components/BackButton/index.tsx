/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Pressable, Text } from 'react-native';
import { twMerge } from 'tailwind-merge';
import ChevronLeft from '@/src/assets/svgs/ChevronLeft';
import { BackButtonProps } from './types';

const BackButton = ({ onBack, label = 'Back', labelClassname }: BackButtonProps) => {
  return (
    <Pressable
      onPress={onBack}
      className="flex-row items-center my-4"
      android_ripple={{ color: 'transparent' }}
    >
      <ChevronLeft />
      <Text className={twMerge('ml-2', labelClassname)}>{label}</Text>
    </Pressable>
  );
};

export default BackButton;
