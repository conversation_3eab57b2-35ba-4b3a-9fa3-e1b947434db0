import React, { useState, useEffect } from 'react';
import {
  Pressable,
  Text,
  View,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import Modal from 'react-native-modal';
import { CustomModalProps } from './types';

const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  title,
  description,
  cancelText = 'Cancel',
  confirmText = 'Delete',
  onConfirm,
  onCancel,
  inputPlaceholder,
  inputValue: externalInputValue,
  onInputChange,
  inputType,
  inputRequired = false,
  inputLabel,
  bodyComponent,
  isConfirming,
  confirmButtonVariant = 'default',
}) => {
  const [inputValue, setInputValue] = useState(externalInputValue || '');

  const isInputEnabled = inputType !== undefined;
  const isConfirmDisabled = isInputEnabled && inputRequired && !inputValue.trim();

  useEffect(() => {
    if (externalInputValue !== undefined) {
      setInputValue(externalInputValue);
    }
  }, [externalInputValue]);

  const handleInputChange = (text: string) => {
    setInputValue(text);
    onInputChange?.(text);
  };

  const handleConfirm = () => {
    if (!isConfirmDisabled) {
      onConfirm(inputValue);
    }
  };

  return (
    <View>
      <Modal
        isVisible={isVisible}
        onBackdropPress={onCancel}
        onBackButtonPress={onCancel}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        backdropOpacity={0.5}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <View className="w-full bg-white rounded-3xl p-6">
            <Text className="text-xl font-normal text-black mb-4">{title}</Text>

            {description && <Text className="text-sm text-black mb-4">{description}</Text>}

            {isInputEnabled && (
              <View className="mb-6">
                {inputLabel && (
                  <Text className="text-sm font-medium text-gray-700 mb-2">{inputLabel}</Text>
                )}
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-3 text-black"
                  placeholder={inputPlaceholder}
                  value={inputValue}
                  onChangeText={handleInputChange}
                  keyboardType={inputType === 'number' ? 'numeric' : 'default'}
                  secureTextEntry={inputType === 'password'}
                  autoCapitalize={inputType === 'email' ? 'none' : 'sentences'}
                />
                {inputRequired && !inputValue.trim() && (
                  <Text className="text-red-500 text-xs mt-1">This field is required</Text>
                )}
              </View>
            )}

            {bodyComponent}

            <View className="flex-row gap-4">
              <Pressable
                className="flex-1 py-3 border border-gray-300 rounded-xl justify-center items-center"
                onPress={onCancel}
              >
                <Text className="text-black text-base">{cancelText}</Text>
              </Pressable>

              <Pressable
                className={`flex-1 py-3 rounded-xl justify-center items-center flex-row gap-2 ${
                  isConfirmDisabled
                    ? 'bg-gray-400'
                    : confirmButtonVariant === 'danger'
                      ? 'bg-red-700'
                      : 'bg-[#5a8d3b]'
                }`}
                onPress={handleConfirm}
                disabled={isConfirmDisabled || isConfirming}
              >
                {isConfirming && <ActivityIndicator size="small" color="#ffffff" />}
                <Text className="text-white text-base">{confirmText}</Text>
              </Pressable>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default CustomModal;
