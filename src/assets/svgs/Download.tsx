/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Download: React.FC<OutlinedIconPropsI> = ({
  width = 2.604,
  height = 2.48,
  stroke = '#448600',
  color,
  strokeWidth = 1.5,
  accessibilityLabel = 'Download',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 21 20"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M10.9587 17.9217C10.8402 18.0512 10.6729 18.125 10.4974 18.125C10.3219 18.125 10.1546 18.0512 10.0361 17.9217L6.7028 14.2759C6.46988 14.0211 6.48758 13.6258 6.74233 13.3929C6.99709 13.16 7.39242 13.1777 7.62533 13.4324L9.8724 15.8902V6.66667C9.8724 6.32149 10.1522 6.04167 10.4974 6.04167C10.8426 6.04167 11.1224 6.32149 11.1224 6.66667V15.8902L13.3695 13.4324C13.6024 13.1777 13.9977 13.16 14.2525 13.3929C14.5072 13.6258 14.5249 14.0211 14.292 14.2759L10.9587 17.9217Z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M3.8724 18.125H17.1224C17.4676 18.125 17.7474 17.8452 17.7474 17.5C17.7474 17.1548 17.4676 16.875 17.1224 16.875H3.8724C3.52722 16.875 3.2474 17.1548 3.2474 17.5C3.2474 17.8452 3.52722 18.125 3.8724 18.125Z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default Download;
