import { apiCall } from '@/src/services/api';
import { OptionAddBodyI, OptionAddResultI } from './types';

export const optionAddAPI = async (
  collection: string,
  payload: OptionAddBodyI,
): Promise<OptionAddResultI> => {
  const route = collection.startsWith('designation-')
    ? '/backend/api/v1/company/designation/options'
    : collection === 'entity'
      ? '/backend/api/v1/company/entity/all/options'
      : collection === 'subVesselType'
        ? '/backend/api/v1/ship/sub-vessel-type/options'
        : collection === 'topic'
          ? '/backend/api/v1/forum/topic/options'
          : collection === 'equipmentCategory'
            ? '/backend/api/v1/ship/equipment-category/options'
            : collection === 'equipmentManufacturer'
              ? '/backend/api/v1/ship/equipment-manufacturer/options'
              : collection === 'equipmentModel'
                ? '/backend/api/v1/ship/equipment-model/options'
                : `/backend/api/v1/company/${collection}/options`;

  return apiCall<OptionAddBodyI, OptionAddResultI>(route, 'POST', {
    isAuth: true,
    payload,
  });
};
