import { apiCall } from '@/src/services/api';
import type { CreateQuestionPayloadI, QuestionCreateResponseI, QuestionI } from './types';

export const createQuestionAPI = async (
  payload: CreateQuestionPayloadI,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    '/backend/api/v1/forum/question',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchQuestionAPI = async (id: string): Promise<QuestionI> => {
  const result = await apiCall<{ id: string }, QuestionI>(
    `/backend/api/v1/community/question/${id}`,
    'GET',
    {
      isAuth: true,
      query: { id },
    },
  );

  return result;
};

export const editQuestionAPI = async (
  payload: CreateQuestionPayloadI,
  questionId: string,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    `/backend/api/v1/community/question/${questionId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
