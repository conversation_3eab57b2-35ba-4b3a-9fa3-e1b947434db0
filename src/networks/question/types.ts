export type QuestionTypeE = 'NORMAL' | 'TROUBLESHOOT';

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type QuestionMediaCreateItemI = {
  fileUrl: string;
  fileExtension: string;
};

export interface CreateQuestionPayloadI {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  equipmentCategory?: IdTypeI;
  equipmentModel?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  topics?: IdTypeI[];
  department?: IdTypeI;
  isAnonymous?: boolean;
  files?: QuestionMediaCreateItemI[];
}

export interface QuestionCreateResponseI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
}

export interface QuestionI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isAnonymous?: boolean;
  createdAt: string;
  updatedAt: string;
  authorId: string;
}
