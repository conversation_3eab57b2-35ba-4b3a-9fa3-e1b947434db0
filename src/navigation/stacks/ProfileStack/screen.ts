import BlockedProfiles from '@/src/screens/BlockedProfiles';
import ChatScreen from '@/src/screens/Chat';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import EditCargoItemScreen from '@/src/screens/EditCargoItem';
import EditCertificationItemScreen from '@/src/screens/EditCertificationItem';
import EditCertificationListScreen from '@/src/screens/EditCertificationList';
import EditDetailScreen from '@/src/screens/EditDetail';
import EditDocumentItemScreen from '@/src/screens/EditDocumentItem';
import EditDocumentsListScreen from '@/src/screens/EditDocumentsList';
import EditEducationItemScreen from '@/src/screens/EditEducationItem';
import EditEducationListScreen from '@/src/screens/EditEducationList';
import EditEquipmentItemScreen from '@/src/screens/EditEquipmentItem';
import EditExperienceItemScreen from '@/src/screens/EditExperienceItem';
import EditExperienceListScreen from '@/src/screens/EditExperienceList';
import EditShipItemScreen from '@/src/screens/EditShipItem';
import EditSkillsListScreen from '@/src/screens/EditSkillsList';
import EditUserProfileScreen from '@/src/screens/EditUserProfile';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import LikesScreen from '@/src/screens/Likes';
import PortsVisitedScreen from '@/src/screens/PortsVisited';
import PrivacyPolicyScreen from '@/src/screens/PrivacyPolicy';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import TermsOfServiceScreen from '@/src/screens/TermsAndCondition';
import UserProfileScreen from '@/src/screens/UserProfile';
import UserSettingScreen from '@/src/screens/UserSettings';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import { ProfileStackParamsListI, StackScreenI } from '../../types';

const UserProfileScreenWithErrorBoundary = withErrorBoundary(UserProfileScreen, {
  title: 'Profile Error',
  subtitle: 'Something went wrong loading the profile. Please try again.',
});

const ConnectionScreenWithErrorBoundary = withErrorBoundary(ConnectionScreen, {
  title: 'Connections Error',
  subtitle: 'Something went wrong loading connections. Please try again.',
});

const ChatScreenWithErrorBoundary = withErrorBoundary(ChatScreen, {
  title: 'Chat Error',
  subtitle: 'Something went wrong in the chat. Please try again.',
});

const LikesScreenWithErrorBoundary = withErrorBoundary(LikesScreen, {
  title: 'Likes Error',
  subtitle: 'Something went wrong loading likes. Please try again.',
});

const CommentScreenWithErrorBoundary = withErrorBoundary(CommentScreen, {
  title: 'Comments Error',
  subtitle: 'Something went wrong loading comments. Please try again.',
});

const UserSettingScreenWithErrorBoundary = withErrorBoundary(UserSettingScreen, {
  title: 'Settings Error',
  subtitle: 'Something went wrong loading settings. Please try again.',
});

const BlockedProfilesWithErrorBoundary = withErrorBoundary(BlockedProfiles, {
  title: 'Blocked Profiles Error',
  subtitle: 'Something went wrong loading blocked profiles. Please try again.',
});

const EditUserProfileScreenWithErrorBoundary = withErrorBoundary(EditUserProfileScreen, {
  title: 'Edit Profile Error',
  subtitle: 'Something went wrong while editing your profile. Please try again.',
});

const EntitySearchScreenWithErrorBoundary = withErrorBoundary(EntitySearchScreen, {
  title: 'Search Error',
  subtitle: 'Something went wrong during search. Please try again.',
});

const EditDetailScreenWithErrorBoundary = withErrorBoundary(EditDetailScreen, {
  title: 'Edit Details Error',
  subtitle: 'Something went wrong while editing details. Please try again.',
});

const EditDocumentsListScreenWithErrorBoundary = withErrorBoundary(EditDocumentsListScreen, {
  title: 'Documents Error',
  subtitle: 'Something went wrong loading your documents. Please try again.',
});

const EditDocumentItemScreenWithErrorBoundary = withErrorBoundary(EditDocumentItemScreen, {
  title: 'Document Edit Error',
  subtitle: 'Something went wrong while editing the document. Please try again.',
});

const EditCertificationListScreenWithErrorBoundary = withErrorBoundary(
  EditCertificationListScreen,
  {
    title: 'Certifications Error',
    subtitle: 'Something went wrong loading your certifications. Please try again.',
  },
);

const EditCertificationItemScreenWithErrorBoundary = withErrorBoundary(
  EditCertificationItemScreen,
  {
    title: 'Certification Edit Error',
    subtitle: 'Something went wrong while editing the certification. Please try again.',
  },
);

const EditExperienceListScreenWithErrorBoundary = withErrorBoundary(EditExperienceListScreen, {
  title: 'Experience Error',
  subtitle: 'Something went wrong loading your experience. Please try again.',
});

const EditExperienceItemScreenWithErrorBoundary = withErrorBoundary(EditExperienceItemScreen, {
  title: 'Experience Edit Error',
  subtitle: 'Something went wrong while editing the experience. Please try again.',
});

const EditEducationListScreenWithErrorBoundary = withErrorBoundary(EditEducationListScreen, {
  title: 'Education Error',
  subtitle: 'Something went wrong loading your education. Please try again.',
});

const EditEducationItemScreenWithErrorBoundary = withErrorBoundary(EditEducationItemScreen, {
  title: 'Education Edit Error',
  subtitle: 'Something went wrong while editing the education. Please try again.',
});

const EditSkillsListScreenWithErrorBoundary = withErrorBoundary(EditSkillsListScreen, {
  title: 'Skills Error',
  subtitle: 'Something went wrong loading your skills. Please try again.',
});

const PortsVisitedScreenWithErrorBoundary = withErrorBoundary(PortsVisitedScreen, {
  title: 'Ports Visited Error',
  subtitle: 'Something went wrong loading ports visited. Please try again.',
});

const EditShipItemScreenWithErrorBoundary = withErrorBoundary(EditShipItemScreen, {
  title: 'Ship Edit Error',
  subtitle: 'Something went wrong while editing the ship details. Please try again.',
});

const EditEquipmentItemScreenWithErrorBoundary = withErrorBoundary(EditEquipmentItemScreen, {
  title: 'Equipment Edit Error',
  subtitle: 'Something went wrong while editing the equipment. Please try again.',
});

const EditCargoItemScreenWithErrorBoundary = withErrorBoundary(EditCargoItemScreen, {
  title: 'Cargo Edit Error',
  subtitle: 'Something went wrong while editing the cargo details. Please try again.',
});

const ShipProfileScreenWithErrorBoundary = withErrorBoundary(ShipProfileScreen, {
  title: 'Ship Profile Error',
  subtitle: 'Something went wrong loading the ship profile. Please try again.',
});

const PrivacyPolicyScreenWithErrorBoundary = withErrorBoundary(PrivacyPolicyScreen, {
  title: 'Privacy Policy Error',
  subtitle: 'Something went wrong loading the privacy policy. Please try again.',
});

const TermsOfServiceScreenWithErrorBoundary = withErrorBoundary(TermsOfServiceScreen, {
  title: 'Terms of Service Error',
  subtitle: 'Something went wrong loading the terms of service. Please try again.',
});

export const screens: StackScreenI<ProfileStackParamsListI>[] = [
  { name: 'UserProfile', component: UserProfileScreenWithErrorBoundary },
  { name: 'OtherUserProfile', component: UserProfileScreenWithErrorBoundary },
  { name: 'Connection', component: ConnectionScreenWithErrorBoundary },
  { name: 'Likes', component: LikesScreenWithErrorBoundary },
  { name: 'Comment', component: CommentScreenWithErrorBoundary },
  { name: 'UserSettings', component: UserSettingScreenWithErrorBoundary },
  { name: 'BlockedUserProfiles', component: BlockedProfilesWithErrorBoundary },
  { name: 'EditUserProfile', component: EditUserProfileScreenWithErrorBoundary },
  { name: 'SearchScreen', component: EntitySearchScreenWithErrorBoundary },
  { name: 'EditDetail', component: EditDetailScreenWithErrorBoundary },
  { name: 'EditDocumentList', component: EditDocumentsListScreenWithErrorBoundary },
  { name: 'EditDocumentItem', component: EditDocumentItemScreenWithErrorBoundary },
  { name: 'EditCertificationList', component: EditCertificationListScreenWithErrorBoundary },
  { name: 'EditCertificationItem', component: EditCertificationItemScreenWithErrorBoundary },
  { name: 'EditExperienceList', component: EditExperienceListScreenWithErrorBoundary },
  { name: 'EditExperienceItem', component: EditExperienceItemScreenWithErrorBoundary },
  { name: 'EditEducationList', component: EditEducationListScreenWithErrorBoundary },
  { name: 'EditEducationItem', component: EditEducationItemScreenWithErrorBoundary },
  { name: 'EditSkillsList', component: EditSkillsListScreenWithErrorBoundary },
  { name: 'PortsVisited', component: PortsVisitedScreenWithErrorBoundary },
  { name: 'EditShipItem', component: EditShipItemScreenWithErrorBoundary },
  { name: 'EditEquipmentItem', component: EditEquipmentItemScreenWithErrorBoundary },
  { name: 'EditCargoItem', component: EditCargoItemScreenWithErrorBoundary },
  { name: 'ShipProfile', component: ShipProfileScreenWithErrorBoundary },
  { name: 'Privacy', component: PrivacyPolicyScreenWithErrorBoundary },
  { name: 'Terms', component: TermsOfServiceScreenWithErrorBoundary },
  { name: 'Chat', component: ChatScreenWithErrorBoundary },
];
