import CommunityRestrictionScreen from '@/src/screens/ComminityRestrictions';
import CommunityScreen from '@/src/screens/Community';
import CommunityBlockedScreen from '@/src/screens/CommunityBlocked';
import CommunityMembersScreen from '@/src/screens/CommunityMembers';
import CommunityQuestionScreen from '@/src/screens/CommunityQuestion';
import CommunityRequestScreen from '@/src/screens/CommunityRequests';
import CreateCommunityScreen from '@/src/screens/CreateCommunity';
import CreateQuestion from '@/src/screens/CreateQuestion';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import ExploreScreen from '@/src/screens/Explore';
import ExploreQnaScreen from '@/src/screens/ExploreQna';
import ExploreTroubleShootScreen from '@/src/screens/ExploreTroubleshoot';
import ForumScreen from '@/src/screens/Forum';
import ForumAnswersScreen from '@/src/screens/ForumAnswers';
import ForumCommentsScreen from '@/src/screens/ForumComments';
import ForumFilterScreen from '@/src/screens/ForumFilter';
import ForumSearchScreen from '@/src/screens/ForumSearch';
import ForumSettingScreen from '@/src/screens/ForumSetting';
import MyCommunitiesScreen from '@/src/screens/MyCommunities';
import PeopleScreen from '@/src/screens/People';
import VotesScreen from '@/src/screens/Votes';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import { LearnCollabStackParamsListI, StackScreenI } from '../../types';

const CreateCommunityScreenWithErrorBoundary = withErrorBoundary(CreateCommunityScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading create community screen. Please try again later.',
});

const CommunityRestrictionScreenWithErrorBoundary = withErrorBoundary(CommunityRestrictionScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading community restriction screen. Please try again later.',
});

const PeopleScreenWithErrorBoundary = withErrorBoundary(PeopleScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading people screen. Please try again later.',
});

const CommunityQuestionScreenWithErrorBoundary = withErrorBoundary(CommunityQuestionScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading community question screen. Please try again later.',
});

const EntitySearchScreenWithErrorBoundary = withErrorBoundary(EntitySearchScreen, {
  title: 'Search Error',
  subtitle: 'Something went wrong during search. Please try again.',
});

const CreateQuestionWithErrorBoundary = withErrorBoundary(CreateQuestion, {
  title: 'Create Question Error',
  subtitle: 'Something went wrong during posting question. Please try again.',
});

const ForumSettingScreenWithErrorBoundary = withErrorBoundary(ForumSettingScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading forum settings. Please try again later.',
});

const CommunityMemberScreenWithErrorBoundary = withErrorBoundary(CommunityMembersScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading member list. Please try again later.',
});

const CommunityBlockedScreenWithErrorBoundary = withErrorBoundary(CommunityBlockedScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading blocked list. Please try again later.',
});

const CommunityRequestScreenWithErrorBoundary = withErrorBoundary(CommunityRequestScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading user requests. Please try again later.',
});

const ForumScreenWithErrorBoundary = withErrorBoundary(ForumScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading forum. Please try again later.',
});

const ForumAnswersScreenWithErrorBoundary = withErrorBoundary(ForumAnswersScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading answers. Please try again later.',
});

const ForumCommentsScreenWithErrorBoundary = withErrorBoundary(ForumCommentsScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading comments. Please try again later.',
});

const ForumSearchScreenWithErrorBoundary = withErrorBoundary(ForumSearchScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading search screen. Please try again later.',
});

const ForumFilterScreenWithErrorBoundary = withErrorBoundary(ForumFilterScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading forum filter. Please try again later.',
});

const VotesScreenWithErrorBoundary = withErrorBoundary(VotesScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading votes. Please try again later.',
});

const CommunityScreenWithErrorBoundary = withErrorBoundary(CommunityScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading community. Please try again later.',
});

const MyCommunitiesScreenWithErrorBoundary = withErrorBoundary(MyCommunitiesScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading my communities. Please try again later.',
});

const ExploreScreenWithErrorBoundary = withErrorBoundary(ExploreScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading explore. Please try again later.',
});

const ExploreQnaScreenWithErrorBoundary = withErrorBoundary(ExploreQnaScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading explore QnA. Please try again later.',
});

const ExploreTroubleShootScreenWithErrorBoundary = withErrorBoundary(ExploreTroubleShootScreen, {
  title: 'Error',
  subtitle: 'Something went wrong loading explore trouble shoot. Please try again later.',
});

export const screens: StackScreenI<LearnCollabStackParamsListI>[] = [
  { name: 'Forum', component: ForumScreenWithErrorBoundary },
  { name: 'ForumAnswers', component: ForumAnswersScreenWithErrorBoundary },
  { name: 'ForumComments', component: ForumCommentsScreenWithErrorBoundary },
  { name: 'ForumSearch', component: ForumSearchScreenWithErrorBoundary },
  { name: 'ForumFilter', component: ForumFilterScreenWithErrorBoundary },
  { name: 'CreateCommunity', component: CreateCommunityScreenWithErrorBoundary },
  { name: 'CommunityRestrictions', component: CommunityRestrictionScreenWithErrorBoundary },
  { name: 'People', component: PeopleScreenWithErrorBoundary },
  { name: 'CommunityQuestion', component: CommunityQuestionScreenWithErrorBoundary },
  { name: 'SearchScreen', component: EntitySearchScreenWithErrorBoundary },
  { name: 'CreateQuestion', component: CreateQuestionWithErrorBoundary },
  { name: 'ForumSetting', component: ForumSettingScreenWithErrorBoundary },
  { name: 'CommunityMembers', component: CommunityMemberScreenWithErrorBoundary },
  { name: 'CommunityBlocked', component: CommunityBlockedScreenWithErrorBoundary },
  { name: 'CommunityRequests', component: CommunityRequestScreenWithErrorBoundary },
  { name: 'Votes', component: VotesScreenWithErrorBoundary },
  { name: 'Community', component: CommunityScreenWithErrorBoundary },
  { name: 'MyCommunities', component: MyCommunitiesScreenWithErrorBoundary },
  { name: 'Explore', component: ExploreScreenWithErrorBoundary },
  { name: 'ExploreQna', component: ExploreQnaScreenWithErrorBoundary },
  { name: 'ExploreTroubleShoot', component: ExploreTroubleShootScreenWithErrorBoundary },
];
