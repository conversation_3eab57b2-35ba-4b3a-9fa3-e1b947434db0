/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { SearchResultI } from '../entitysearch/types';

export type QuestionTypeE = 'NORMAL' | 'TROUBLESHOOT';

export type QuestionMediaCreateItemI = {
  fileUrl: string;
  fileExtension: string;
};

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type QuestionFormDataI = {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isAnonymous: boolean;
  topics: SearchResultI[];
  equipmentCategory: SearchResultI | null;
  equipmentModel: SearchResultI | null;
  equipmentManufacturer: SearchResultI | null;
  department: SearchResultI | null;
  files: QuestionMediaCreateItemI[];
};

export type QuestionCreatePayloadI = {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  equipmentCategory?: IdTypeI;
  equipmentModel?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  topics?: IdTypeI[];
  department?: IdTypeI;
  isAnonymous?: boolean;
  files?: QuestionMediaCreateItemI[];
};

export type QuestionCreateResponseI = {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
};

export type QuestionStateI = {
  formData: QuestionFormDataI;
  loading: boolean;
  error: string | null;
};
