import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { handleError } from '@/src/utilities/errors/errors';
import APIResError from '@/src/errors/networks/APIResError';
import { CargoCreateUpdatePayloadI } from '@/src/screens/EditCargoItem/components/EditCargoItem/types';
import { EquipmentCreateUpdatePayloadI } from '@/src/screens/EditEquipmentItem/components/EditEquipmentItem/types';
import {
  generateAddExperiencePayload,
  generateEditExperiencePayload,
} from '@/src/screens/EditExperienceItem/components/utils';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { apiCall } from '@/src/services/api';
import { addExperience } from '@/src/networks/experience/experience';
import {
  addCargoForShipExperience,
  addEquipmentCategoryForShipExperience,
  addShipForExperience,
} from '@/src/networks/experienceShip.ts/ship';
import { SearchResultI } from '../entitysearch/types';
import {
  ExperienceFetchParamsI,
  ExperienceFetchReponseI,
  ExperienceStateI,
  ExperienceFetchForClientResultI,
  ExperienceItemI,
  ExperienceModuleCreateOneParamsI,
  OprTypeE,
  ExperienceModuleUpdateOneParamsI,
} from './types';

const initialState: ExperienceStateI = {
  experiences: { data: [], total: 0, portVisits: { total: 0, data: [] } },
  loading: false,
  error: null,
};

export const fetchExperiencesAsync = createAsyncThunk<
  ExperienceFetchReponseI,
  ExperienceFetchParamsI,
  { rejectValue: string }
>('experience/fetchExperiences', async ({ profileId }, { rejectWithValue }) => {
  try {
    return await apiCall<ExperienceFetchParamsI, ExperienceFetchReponseI>(
      '/backend/api/v1/career/profile-experiences',
      'GET',
      {
        isAuth: true,
        query: { profileId },
      },
    );
  } catch (error) {
    return rejectWithValue(
      (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
        'Failed to fetch experiences',
    );
  }
});

export const fetchSingleExperienceAsync = createAsyncThunk<
  { response: ExperienceFetchForClientResultI; id: string },
  { id: string },
  { rejectValue: string }
>('experience/fetchSingleExperience', async ({ id }, { rejectWithValue }) => {
  try {
    const response = await apiCall<{ id: string }, ExperienceFetchForClientResultI>(
      `/backend/api/v1/career/profile-experience/${id}`,
      'GET',
      {
        isAuth: true,
        query: { id },
      },
    );
    return { response, id };
  } catch (error) {
    return rejectWithValue(
      (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
        'Failed to fetch experience',
    );
  }
});

export const addExperienceAsync = createAsyncThunk<
  unknown,
  { payload: any[] },
  { rejectValue: string }
>('experience/addExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    const _response = await addExperience(payload);
  } catch (error: unknown) {
    if (error instanceof APIResError) {
      return rejectWithValue(error.message || 'Failed to add experience');
    }
    return rejectWithValue('Failed to add experience');
  }
});

export const editExperienceAsync = createAsyncThunk<
  ExperienceFetchForClientResultI,
  {
    experience: ExperienceItemI;
    selectedExperience: ExperienceFetchForClientResultI | undefined;
  },
  { rejectValue: string }
>('experience/editExperience', async ({ experience, selectedExperience }, { rejectWithValue }) => {
  try {
    const payload = generateEditExperiencePayload(experience, selectedExperience);
    await apiCall<ExperienceModuleUpdateOneParamsI, void>(
      '/backend/api/v1/career/profile-experience',
      'POST',
      {
        isAuth: true,
        payload,
      },
    );
    return experience;
  } catch (error: unknown) {
    handleError(error);
    if (error instanceof APIResError) {
      return rejectWithValue(error.message || 'Failed to update experience');
    }
    return rejectWithValue('Failed to update experience');
  }
});

export const deleteExperienceAsync = createAsyncThunk<
  string,
  { id: string },
  { rejectValue: string }
>('experience/deleteExperience', async ({ id }, { rejectWithValue }) => {
  try {
    const payload = [{ id, opr: 'DELETE' as OprTypeE }];
    await apiCall<typeof payload, void>('/backend/api/v1/career/profile-experience', 'POST', {
      isAuth: true,
      payload,
    });
    return id;
  } catch (error: unknown) {
    if (error instanceof APIResError) {
      return rejectWithValue(error.message || 'Failed to delete experience');
    }
    return rejectWithValue('Failed to delete experience');
  }
});

export const addShipExperience = createAsyncThunk<
  string[],
  { payload: ShipCreateEditPayloadI[] },
  { rejectValue: string }
>('experience/addShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    const response = await addShipForExperience(payload);
    return response;
  } catch (error: unknown) {
    if (error instanceof Error) {
      return rejectWithValue(error.message || 'Something went wrong');
    }
    return rejectWithValue('Something went wrong');
  }
});

export const addEquipmentCategoryShipExperience = createAsyncThunk<
  string[],
  { payload: EquipmentCreateUpdatePayloadI },
  { rejectValue: string }
>('experience/addEquipmentCategoryShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    return await addEquipmentCategoryForShipExperience(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Something went wrong');
  }
});

export const addCargoShipExperience = createAsyncThunk<
  string[],
  { payload: CargoCreateUpdatePayloadI },
  { rejectValue: string }
>('experience/addCargoShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    return await addCargoForShipExperience(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Something went wrong');
  }
});

const experienceSlice = createSlice({
  name: 'experience',
  initialState,
  reducers: {
    resetExperienceState: () => initialState,
    setProfileId: (state, action: PayloadAction<string>) => {
      state.profileId = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateExperienceLocally: (state, action: PayloadAction<ExperienceFetchForClientResultI>) => {
      const index = state.experiences.data.findIndex(
        (exp: ExperienceFetchForClientResultI) => exp.id === action.payload.id,
      );
      if (index !== -1) {
        state.experiences.data[index] = action.payload;
      }
    },
    addExperienceLocally: (state, action: PayloadAction<ExperienceFetchForClientResultI>) => {
      state.experiences.data.push(action.payload);
      state.experiences.total += 1;
    },
    removeExperienceLocally: (state, action: PayloadAction<string>) => {
      state.experiences.data = state.experiences.data.filter(
        (exp: ExperienceFetchForClientResultI) => exp.id !== action.payload,
      );
      state.experiences.total -= 1;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchExperiencesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExperiencesAsync.fulfilled, (state, action) => {})
      .addCase(fetchExperiencesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch experiences';
      })
      .addCase(fetchSingleExperienceAsync.fulfilled, (state, action) => {
        const { id, response } = action.payload;
        const index = state.experiences.data.findIndex(
          (exp: ExperienceFetchForClientResultI) => exp.id === id,
        );
        if (index !== -1) {
          state.experiences.data[index] = {
            ...state.experiences.data[index],
            designations: response.designations,
          };
        }
      })
      .addCase(editExperienceAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editExperienceAsync.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.experiences.data.findIndex(
          (exp: ExperienceFetchForClientResultI) => exp.id === action.payload.id,
        );
        if (index !== -1) {
          state.experiences.data[index] = action.payload;
        }
      })
      .addCase(editExperienceAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to update experience';
      })
      .addCase(deleteExperienceAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteExperienceAsync.fulfilled, (state, action) => {})
      .addCase(deleteExperienceAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to delete experience';
      })

      .addCase(addShipExperience.fulfilled, (state, action: PayloadAction<string[]>) => {})
      .addCase(
        addEquipmentCategoryShipExperience.fulfilled,
        (state, action: PayloadAction<string[]>) => {},
      )
      .addCase(addCargoShipExperience.fulfilled, (state, action: PayloadAction<string[]>) => {});
  },
});

export const {
  resetExperienceState,
  setProfileId,
  clearError,
  updateExperienceLocally,
  addExperienceLocally,
  removeExperienceLocally,
} = experienceSlice.actions;

export default experienceSlice.reducer;
