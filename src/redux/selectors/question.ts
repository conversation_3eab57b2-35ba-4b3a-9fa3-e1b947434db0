/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';
import type { QuestionStateI } from '../slices/question/types';

export const selectQuestionState = (state: RootState): QuestionStateI => state.question;

export const selectQuestionFormData = createSelector(
  [selectQuestionState],
  (questionState) => questionState.formData
);

export const selectQuestionType = createSelector(
  [selectQuestionFormData],
  (formData) => formData.type
);


export const selectQuestionDepartment = createSelector(
  [selectQuestionFormData],
  (formData) => formData.department
);

export const selectQuestionIsAnonymous = createSelector(
  [selectQuestionFormData],
  (formData) => formData.isAnonymous
);

export const selectQuestionCommunityId = createSelector(
  [selectQuestionFormData],
  (formData) => formData.communityId
);

export const selectQuestionFiles = createSelector(
  [selectQuestionFormData],
  (formData) => formData.files
);

export const selectQuestionLoading = createSelector(
  [selectQuestionState],
  (questionState) => questionState.loading
);

export const selectQuestionError = createSelector(
  [selectQuestionState],
  (questionState) => questionState.error
);

// Forum questions selectors
export const selectForumQuestions = createSelector(
  [selectQuestionState],
  (questionState) => questionState.forumQuestions
);

export const selectForumQuestionsLoading = createSelector(
  [selectQuestionState],
  (questionState) => questionState.forumQuestionsLoading
);

export const selectForumQuestionsRefreshing = createSelector(
  [selectQuestionState],
  (questionState) => questionState.forumQuestionsRefreshing
);

export const selectForumQuestionsPagination = createSelector(
  [selectQuestionState],
  (questionState) => questionState.forumQuestionsPagination
);

export const selectForumQuestionsFilters = createSelector(
  [selectQuestionState],
  (questionState) => questionState.forumQuestionsFilters
);
