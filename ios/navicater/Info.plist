<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Navicater</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>NSCameraUsageDescription</key>
	<string>Take photos and videos for messaging</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Record audio messages and videos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Select and share images and videos</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Save photos and videos</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>0.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.900862906994-urn1h2m4r4db8t3pnlj0dp43jndp79f1</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>navicater</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>455</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
	<key>NSAllowsArbitraryLoadsInWebContent</key>
	<true/>
	<key>NSAllowsLocalNetworking</key>
	<true/>
	<key>NSExceptionDomains</key>
	<dict>
		<key>api.b2c.navicater.com</key>
		<dict>
		<key>NSIncludesSubdomains</key>
		<true/>
		<key>NSExceptionAllowsInsecureHTTPLoads</key>
		<true/>
		<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
		<false/>
		</dict>
	</dict>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Show current location on map.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photos to let you attach images</string>
    <key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
