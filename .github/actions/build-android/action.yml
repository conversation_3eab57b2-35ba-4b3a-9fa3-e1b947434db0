name: 'Build Android App'
description: 'Builds an Android AAB'

inputs:
  version:
    description: 'Semantic version'
    required: true
  build-number:
    description: 'Build number'
    required: true
  keystore-password:
    description: 'Keystore password'
    required: true
  keystore-alias:
    description: 'Keystore alias'
    required: true
  key-password:
    description: 'Key password'
    required: true
  keystore-base64:
    description: 'Base64-encoded keystore'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'

    - name: Install Node.js Dependencies
      shell: bash
      run: |
        echo "📦 Installing Node.js dependencies"
        npm ci

    - name: Create .env file
      shell: bash
      run: |
        echo "🔐 Creating .env file"
        cat <<EOF > .env
        API_KEY=${{ env.API_KEY }}
        BASE_URL=${{ env.BASE_URL }}
        AI_URL=${{ env.AI_URL }}
        FIREBASE_ANDROID_APP_ID=${{ env.FIREBASE_ANDROID_APP_ID }}
        FIREBASE_API_KEY=${{ env.FIREBASE_API_KEY }}
        FIREBASE_AUTH_DOMAIN=${{ env.FIREBASE_AUTH_DOMAIN }}
        FIREBASE_DATABASE_URL=${{ env.FIREBASE_DATABASE_URL }}
        FIREBASE_IOS_APP_ID=${{ env.FIREBASE_IOS_APP_ID }}
        FIREBASE_MESSAGING_SENDER_ID=${{ env.FIREBASE_MESSAGING_SENDER_ID }}
        FIREBASE_PROJECT_ID=${{ env.FIREBASE_PROJECT_ID }}
        FIREBASE_STORAGE_BUCKET=${{ env.FIREBASE_STORAGE_BUCKET }}
        IOS_CLIENT_ID=${{ env.IOS_CLIENT_ID }}
        SENTRY_DSN_URL=${{ env.SENTRY_DSN_URL }}
        WEB_CLIENT_ID=${{ env.WEB_CLIENT_ID }}
        ENV=${{ env.ENV }}
        EOF
        cat .env

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Install Dependencies
      run: |
        echo "📦 Installing Gradle dependencies"
        cd android
        ./gradlew dependencies
      shell: bash

    - name: Update version in build.gradle
      shell: bash
      run: |
        echo "🔧 Updating version in build.gradle"
        cd android/app
        sed -i "s/versionCode [0-9]\+/versionCode ${{ inputs.build-number }}/" build.gradle
        sed -i "s/versionName \"[0-9]\+\.[0-9]\+\.[0-9]\+\"/versionName \"${{ inputs.version }}\"/" build.gradle
        grep -A 2 "versionCode" build.gradle

    - name: Commit and Push Version Update
      shell: bash
      run: |
        echo "🔐 Committing updated version info"
        git config --global user.name "GitHub Actions"
        git config --global user.email "<EMAIL>"
        git add android/app/build.gradle
        git commit -m "chore: bump Android version to ${{ inputs.version }} (${{ inputs.build-number }})"
        git push || { echo "❌ Git push failed. Stopping workflow."; exit 1; }

    - name: Decode Keystore
      shell: bash
      run: |
        echo "🔓 Decoding keystore"
        echo "${KEYSTORE_BASE64}" | base64 --decode > android/app/release.keystore
        ls -l android/app/release.keystore || { echo "❌ Keystore file missing!"; exit 1; }
      env:
        KEYSTORE_BASE64: ${{ inputs.keystore-base64 }}

    - name: Build Android Release AAB
      shell: bash
      run: |
        echo "🏗️ Building Android AAB"
        cd android
        ./gradlew bundleRelease --stacktrace --info
      env:
        KEYSTORE_PASSWORD: ${{ inputs.keystore-password }}
        KEYSTORE_ALIAS: ${{ inputs.keystore-alias }}
        KEY_PASSWORD: ${{ inputs.key-password }}

    - name: Upload Release AAB artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-release-bundle
        path: android/app/build/outputs/bundle/release/app-release.aab
        retention-days: 7
